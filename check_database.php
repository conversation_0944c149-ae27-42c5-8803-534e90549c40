<?php
// Database Check Script for CBT Application
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>CBT Database Check</h2>";

// Include the database connection
include "config/server.php";

echo "<h3>1. Database Connection Test</h3>";
if (isset($database) && $database) {
    echo "✅ Database connection established<br>";
    echo "Database name: " . (isset($db) ? $db : 'Unknown') . "<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

echo "<h3>2. Check if cbt_admin table exists</h3>";
$check_table = mysql_query("SHOW TABLES LIKE 'cbt_admin'");
if (mysql_num_rows($check_table) > 0) {
    echo "✅ cbt_admin table exists<br>";
    
    // Check if table has data
    $check_data = mysql_query("SELECT COUNT(*) as count FROM cbt_admin");
    $count_result = mysql_fetch_array($check_data);
    $record_count = $count_result['count'];
    
    echo "Records in cbt_admin: " . $record_count . "<br>";
    
    if ($record_count > 0) {
        echo "✅ cbt_admin table has data<br>";
        
        // Show the first record
        $admin_data = mysql_query("SELECT * FROM cbt_admin LIMIT 1");
        $admin = mysql_fetch_array($admin_data);
        
        echo "<h4>Admin Configuration:</h4>";
        echo "School: " . (isset($admin['XSekolah']) ? $admin['XSekolah'] : 'Not set') . "<br>";
        echo "Banner: " . (isset($admin['XBanner']) ? $admin['XBanner'] : 'Not set') . "<br>";
        echo "Color: " . (isset($admin['XWarna']) ? $admin['XWarna'] : 'Not set') . "<br>";
        echo "Logo: " . (isset($admin['XLogo']) ? $admin['XLogo'] : 'Not set') . "<br>";
        
    } else {
        echo "❌ cbt_admin table is empty<br>";
        echo "<p><strong>Solution:</strong> You need to import the database structure and initial data.</p>";
        echo "<p>Run one of these SQL files:</p>";
        echo "<ul>";
        echo "<li>config/cbt.sql</li>";
        echo "<li>config/beesmartv3_rev3.sql</li>";
        echo "</ul>";
    }
} else {
    echo "❌ cbt_admin table does not exist<br>";
    echo "<p><strong>Solution:</strong> You need to import the database structure.</p>";
    echo "<p>Run one of these SQL files:</p>";
    echo "<ul>";
    echo "<li>config/cbt.sql</li>";
    echo "<li>config/beesmartv3_rev3.sql</li>";
    echo "</ul>";
}

echo "<h3>3. Check other important tables</h3>";
$important_tables = ['cbt_user', 'cbt_siswa', 'cbt_mapel', 'cbt_kelas', 'cbt_soal'];

foreach ($important_tables as $table) {
    $check = mysql_query("SHOW TABLES LIKE '$table'");
    if (mysql_num_rows($check) > 0) {
        $count_query = mysql_query("SELECT COUNT(*) as count FROM $table");
        $count_data = mysql_fetch_array($count_query);
        echo "✅ $table exists (" . $count_data['count'] . " records)<br>";
    } else {
        echo "❌ $table does not exist<br>";
    }
}

echo "<h3>4. Database Import Instructions</h3>";
echo "<p>If tables are missing or empty, follow these steps:</p>";
echo "<ol>";
echo "<li>Open phpMyAdmin or your MySQL client</li>";
echo "<li>Create a database named 'cbt' if it doesn't exist</li>";
echo "<li>Import one of these files:</li>";
echo "<ul>";
echo "<li><code>config/cbt.sql</code> - Main database structure</li>";
echo "<li><code>config/beesmartv3_rev3.sql</code> - Alternative structure</li>";
echo "</ul>";
echo "<li>After import, refresh this page to verify</li>";
echo "</ol>";

echo "<h3>5. Quick Database Setup</h3>";
echo "<p>You can also use the built-in database creation tool:</p>";
echo "<p><a href='panel/pages/buat_database.php' target='_blank'>Create Database</a></p>";

?>
