<?php
// Database Initialization Script for CBT Application
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>CBT Database Initialization</h2>";

// Include the database connection
include "config/server.php";

if (isset($_POST['init_db'])) {
    echo "<h3>Initializing Database...</h3>";
    
    // Create basic admin record if cbt_admin table exists but is empty
    $check_admin = mysql_query("SELECT COUNT(*) as count FROM cbt_admin");
    if ($check_admin) {
        $admin_count = mysql_fetch_array($check_admin);
        if ($admin_count['count'] == 0) {
            $insert_admin = mysql_query("INSERT INTO cbt_admin (XSekolah, XAdmin, XBanner, XLogo, XWarna, XTingkat) VALUES 
                ('CBT School', 'Administrator', 'banner.jpg', 'logo.png', '#007bff', 'SMA')");
            
            if ($insert_admin) {
                echo "<p>✅ Default admin record created</p>";
            } else {
                echo "<p>❌ Failed to create admin record: " . mysql_error() . "</p>";
            }
        } else {
            echo "<p>✅ Admin records already exist</p>";
        }
    }
    
    // Create a test user if cbt_user table exists
    $check_user = mysql_query("SELECT COUNT(*) as count FROM cbt_user WHERE Username = 'admin'");
    if ($check_user) {
        $user_count = mysql_fetch_array($check_user);
        if ($user_count['count'] == 0) {
            $password = md5('admin123');
            $insert_user = mysql_query("INSERT INTO cbt_user (Username, Password, NIP, Nama, HP, Email, login, Status) VALUES 
                ('admin', '$password', '123456789', 'Administrator', '08123456789', '<EMAIL>', '1', '1')");
            
            if ($insert_user) {
                echo "<p>✅ Default admin user created (Username: admin, Password: admin123)</p>";
            } else {
                echo "<p>❌ Failed to create admin user: " . mysql_error() . "</p>";
            }
        } else {
            echo "<p>✅ Admin user already exists</p>";
        }
    }
    
    echo "<p><strong>Database initialization completed!</strong></p>";
    echo "<p><a href='panel/'>Go to Panel</a></p>";
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBT Database Initialization</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Database Initialization</h3>
                </div>
                <div class="card-body">
                    <p>This script will initialize the CBT database with basic data if the tables exist but are empty.</p>
                    
                    <h5>What this script does:</h5>
                    <ul>
                        <li>Creates a default admin configuration record</li>
                        <li>Creates a default admin user (Username: admin, Password: admin123)</li>
                        <li>Sets up basic system settings</li>
                    </ul>
                    
                    <div class="alert alert-warning">
                        <strong>Important:</strong> Make sure you have already imported the database structure from one of these files:
                        <ul>
                            <li>config/cbt.sql</li>
                            <li>config/beesmartv3_rev3.sql</li>
                        </ul>
                    </div>
                    
                    <form method="post">
                        <button type="submit" name="init_db" class="btn btn-primary">Initialize Database</button>
                        <a href="check_database.php" class="btn btn-info">Check Database Status</a>
                        <a href="panel/" class="btn btn-success">Go to Panel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
