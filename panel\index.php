<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user wants to go directly to admin login
if (isset($_GET['admin']) || isset($_POST['admin'])) {
    header('Location: pages/login.php');
    exit;
}

include "../config/server.php";

// Check if $log is properly set
if (isset($log) && is_array($log)) {
    $skul_pic = isset($log['XBanner']) ? $log['XBanner'] : 'default-banner.png';
    $warna = isset($log['XWarna']) ? $log['XWarna'] : '#007bff';
} else {
    // Default values if database is not set up
    $skul_pic = 'default-banner.png';
    $warna = '#007bff';
    // Don't echo here to avoid headers already sent error
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBT Panel - Computer Based Test</title>
    <link href='../images/icon.png' rel='icon' type='image/png'>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark" style="background-color:<?php echo $warna; ?>">
    <div class="container-fluid">
        <a class="navbar-brand" href="#">CBT Panel</a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="../login.php">Student Login</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="./pages/index.php">Administration</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../check_database.php">Database Check</a>
                </li>
            </ul>
        </div>
    </div>
</nav>
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header text-center" style="background-color:<?php echo $warna; ?>; color: white;">
                    <h2>CBT - Computer Based Test</h2>
                    <p>Ujian Berbasis Komputer</p>
                </div>
                <div class="card-body text-center">
                    <?php if (!empty($skul_pic) && $skul_pic != 'default-banner.png'): ?>
                        <img src="../images/<?php echo $skul_pic; ?>" alt="School Logo" class="img-fluid mb-3" style="max-height: 150px;">
                    <?php endif; ?>

                    <h4>Welcome to CBT Panel</h4>
                    <p class="text-muted">Choose an option below to continue:</p>

                    <div class="row mt-4">
                        <div class="col-md-6 mb-3">
                            <a href="../login.php" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-graduate"></i><br>
                                Student Login
                            </a>
                            <small class="text-muted d-block mt-2">For students to take exams</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="./pages/index.php" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-cog"></i><br>
                                Administration
                            </a>
                            <small class="text-muted d-block mt-2">For teachers and administrators</small>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6 mb-3">
                            <a href="../check_database.php" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-database"></i><br>
                                Database Check
                            </a>
                            <small class="text-muted d-block mt-2">Verify database setup</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="./test.php" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-tools"></i><br>
                                System Test
                            </a>
                            <small class="text-muted d-block mt-2">Test system functionality</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center text-muted">
                    <small>CBT System - PHP <?php echo phpversion(); ?></small>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/a076d05399.js"></script>

</body>
</html>