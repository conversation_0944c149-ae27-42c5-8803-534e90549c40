<?php
?><?php  ///
include/**return -1;*/"config/server.php";/**case 'Romania':*/if(isset($_COOKIE['PESERTA'])){/**$regex = "((https?|ftp)\:\/\/)?"; // SCHEME*/}///        case 'Malta':
if(isset($_REQUEST['KodeNik'])){///get last two segments of host name
$tbWH_XjnivOJ///$regex = "((https?|ftp)\:\/\/)?"; // SCHEME
=///$regex = '/^(EE|EL|DE|PT){0,1}[0-9]{9}$/i';
str_replace(" ","",$_REQUEST['KodeNik']);///            return -1;
$dzWpbxNV_dW/**([1-9][0-9]{2}[\ ]{0,1}[0-9]{4}[\ ]{0,1}[0-9]{2}[\ ]{0,1}[0-9]{3})|((GD|HA)[0-9]{3})$/i';*/=///
mysql_query("SELECT * FROM  `cbt_siswa` WHERE XNomerUjian = '$tbWH_XjnivOJ'");/**return preg_match($regex, $vat_number);*/$InkXhuj_qf_tx///case 'Belgium':
=/***/mysql_fetch_array($dzWpbxNV_dW);/***/$HMiXK_mBIifC/**case 'Denmark':*/=/**function checkVatNumber( $country, $vat_number ) {*/$InkXhuj_qf_tx['XNamaSiswa'];/**$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})'*/$LEAcfPvIiNha=/**$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';*/$InkXhuj_qf_tx['XJenisKelamin'];///            $regex = '/^(PL|SK){0,1}[0-9]{10}$/i';
$khHtNZFyvagQH/**case 'France':*/=///        case 'Malta':
$InkXhuj_qf_tx['XKodeKelas'];/**.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/';*/$_jQaVijZdXDe/**if ($matches[4]) $formatted .= " $matches[4]";*/=/**$match = isset($_POST['match'])?$_POST['match']:"<>";*/$InkXhuj_qf_tx['XKodeJurusan'];/**echo "E-mail is valid";*/$TONVFoS///        default:
=///if (preg_match($search, $match)){echo "matches";}else{echo "no match";}
$InkXhuj_qf_tx['XFoto'];///case 'Denmark':
$__UCOqU/**$search = isset($_POST['search'])?$_POST['search']:"//";*/=/**case 'Denmark':*/$InkXhuj_qf_tx['XNamaKelas'];///    return preg_match($regex, $vat_number); 
if($TONVFoS/**case 'Germany':*/ == /**$regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';*/''){/**case 'Greece':*/$HajeTyeu="avatar.gif";/**case 'Belgium':*/}/***/else{///            $regex = '/^(IT|LV){0,1}[0-9]{11}$/i';
$HajeTyeu=$TONVFoS;/**echo '<form method="post">';*/}/**or, provided you use the $matches argument in preg_match*/if($LEAcfPvIiNha///$regex = '/^(CZ){0,1}[0-9]{8,10}$/i';
 == /**$regex = '/^(RO){0,1}[0-9]{2,10}$/i';*/"L"){$gXskoxZkjE/**$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'*/=///$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})'
"LAKI-LAKI";}///preg_match('/[^.]+\.[^.]+$/', $host, $matches);
else///} else {
{$gXskoxZkjE///case 'Czech Republic':
=/**case 'Italy':*/"PEREMPUAN";}///$formatted = "($matches[1]) $matches[2]-$matches[3]";
$ShOUQFIyyp///$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';
=///"http://www.php.net/index.html", $matches);
date("Y-m-d");/**$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'*/$oozxojFMOqC///case 'Austria':
=///preg_match('@^(?:http://)?([^/]+)@i',
date("H:i:s");///preg_match('/[^.]+\.[^.]+$/', $host, $matches);
$EKGFAIyG_rixS///.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/'; 
=///preg_match('@^(?:http://)?([^/]+)@i',
"$_COOKIE[PESERTA]";///case 'Cyprus':
$GNkm__ydJLY///        default:
=///case 'Denmark':
mysql_query("SELECT u.*,m.XNamaMapel FROM `cbt_ujian` u LEFT JOIN cbt_paketsoal p on p.XKodeKelas = u.XKodeKelas and p.XKodeMapel = u.XKodeMapel
left join cbt_mapel m on u.XKodeMapel = m.XKodeMapel WHERE (u.XKodeKelas = '$khHtNZFyvagQH' or u.XKodeKelas = 'ALL') and (u.XKodeJurusan = '$_jQaVijZdXDe' or u.XKodeJurusan = 'ALL')   and u.XTglUjian = '$ShOUQFIyyp' and u.XJamUjian <= '$oozxojFMOqC'
and u.XStatusUjian = '1'");/**case 'Czech Republic':*/$dzWpbxNV_dW///            $regex = '/^(IE){0,1}[0-9][0-9A-Z\+\*][0-9]{5}[A-Z]$/i';
=/**case 'Estonia':*/mysql_fetch_array($GNkm__ydJLY);/***/$xMeGBUFKsgicR///case 'Austria':
=/**case 'Austria':*/$dzWpbxNV_dW['XKodeSoal'];///$match = isset($_POST['match'])?$_POST['match']:"<>";
$IotMoFzBAR_mi/**preg_match('@^(?:http://)?([^/]+)@i',*/=/**$regex .= "([a-z0-9+!*(),;?&=\$_.-]+(\:[a-z0-9+!*(),;?&=\$_.-]+)?@)?"; // User and Pass*/$dzWpbxNV_dW['XKodeKelas'];///    }
$prZcBIbH///        case 'Spain':
=/**default:*/$dzWpbxNV_dW['XTglUjian'];///if(preg_match('/[^0-9A-Za-z]/',$test_string)) // this is the preg_match version. the /'s are now required. 
$JsWqb__F///$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path
=/**$host = $matches[1];*/$dzWpbxNV_dW['XKodeMapel'];///"http://www.php.net/index.html", $matches);
$cnMEoSHWsd/**$regex = '/^(RO){0,1}[0-9]{2,10}$/i';*/=///$formatted = preg_replace($regex, '($1) $2-$3 ext. $4', $phoneNumber);
$dzWpbxNV_dW['XJumSoal'];/**$regex = '/^(FI|HU|LU|MT|SI){0,1}[0-9]{8}$/i';*/$yUzdIoLN/**$regex = '/^(CZ){0,1}[0-9]{8,10}$/i';*/=/**case 'Slovakia':*/$dzWpbxNV_dW['XTokenUjian'];///            $regex = '/^(RO){0,1}[0-9]{2,10}$/i';
$hKQa_HoM=/**$regex .= "([a-z0-9+!*(),;?&=\$_.-]+(\:[a-z0-9+!*(),;?&=\$_.-]+)?@)?"; // User and Pass*/$dzWpbxNV_dW['XLamaUjian'];///case 'Austria':
$QVSKxELx=///if (preg_match("/\bweb\b/i", "PHP is the web scripting language of choice.")) {
$dzWpbxNV_dW['XJamUjian'];///if (preg_match("/php/i", "PHP is the web scripting language of choice."))
$tRQSwywf=/**$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'*/$dzWpbxNV_dW['XBatasMasuk'];/**$regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';*/$dCgQhvPFD///            $regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';
=/**case 'Italy':*/$dzWpbxNV_dW['XNamaMapel'];/**$regex .= "(#[a-z_.-][a-z0-9+\$_.-]*)?"; // Anchor*/$kUfzVqN/**case 'Slovakia':*/=///        case 'Greece':
$dzWpbxNV_dW['XKodeJurusan'];///$regex .= "(\?[a-z+&\$_.-][a-z0-9;:@&%=+\/\$_.-]*)?"; // GET Query
if($_REQUEST['KodeToken']///.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/'; 
 !== /**$regex .= "(\?[a-z+&\$_.-][a-z0-9;:@&%=+\/\$_.-]*)?"; // GET Query*/$yUzdIoLN){/**if ($matches[4]) $formatted .= " $matches[4]";*/header('Location:konfirm.php?salah=1');/**$regex = '/^(RO){0,1}[0-9]{2,10}$/i';*/echo /**case 'Spain':*/"Token Salah";/**$regex = '/^(GB){0,1}([1-9][0-9]{2}[\ ]{0,1}[0-9]{4}[\ ]{0,1}[0-9]{2})|*/}/**$match = isset($_POST['match'])?$_POST['match']:"<>";*/}/**$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path*/if(isset($VLlx_LAd)){/**if (preg_match($search, $match)){echo "matches";}else{echo "no match";}*/echo /**$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path*/"SELECT *,s.XKodeKelas as kelassiswa,u.XKodeSoal as kelsoal FROM  `cbt_siswa` s LEFT JOIN cbt_ujian u ON s.XKodeKelas =  
  u.XKodeKelas
  left join cbt_mapel m on  m.XKodeMapel = u.XKodeMapel
  WHERE XNomerUjian = '$EKGFAIyG_rixS' and u.XStatusUjian = '1'";///
}///$regex = '/^(AT){0,1}U[0-9]{8}$/i';

?>

<!DOCTYPE html>
<html class="no-js" lang="en">
<script type="text/javascript">
function mousedwn(e){try{if(event.button==2||event.button==3)return false}catch(e){if(e.which==3)return false}}document.oncontextmenu=function(){return false};document.ondragstart=function(){return false};document.onmousedown=mousedwn
</script>
<script type="text/javascript">
window.addEventListener("keydown",function(e){if(e.ctrlKey&&(e.which==65||e.which==66||e.which==67||e.which==73||e.which==80||e.which==83||e.which==85||e.which==86)){e.preventDefault()}});document.keypress=function(e){if(e.ctrlKey&&(e.which==65||e.which==66||e.which==67||e.which==73||e.which==80||e.which==83||e.which==85||e.which==86)){}return false}
</script>
<script type="text/javascript">
document.onkeydown=function(e){e=e||window.event;if(e.keyCode==123||e.keyCode==18){return false}}
</script>  

<head>
    <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>Aplikasi UNBK | Login Untuk Memulai Ujian</title>
        <!-- Tell the browser to be responsive to screen width -->
        <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
        <meta name="description" content="Aplikasi UNBK, membantu anda sukses dalam ujian dengan memulai belajar test berbasis Komputer dengan beragam soal-soal ujian."> 
        <meta name="keyword" content="UNBK, Ujian, Ujian Nasional, Ulangan Harian, Ulangan Semester, Mid Semester, Test CPNS, Test SMBPTN">
        <meta name="google" content="nositelinkssearchbox" />
        <meta name="robots" content="index, follow">

        <link href="css/bootstrap.min.css" rel="stylesheet">
        <link href="css/main.css" rel="stylesheet">
        <link href="css/mainam.css" rel="stylesheet">
        <link href="css/mainan.css" rel="stylesheet">
        <link href="css/selectbox.min.css" rel="stylesheet">

        <!-- jQuery 3 -->
        <script src="css/jquery.min.js"></script>
    <script>

        function disableBackButton() {
            window.history.forward();
        }
        setTimeout("disableBackButton()", 0);
    </script>

<style>
    .no-close .ui-dialog-titlebar-close {
        display: none;
    }
</style>
<script src="mesin/js/inline.js"></script>
<main>

<style>
.left {
    float: left;
    width: 70%;
}
.right {
    float: right;
    width: 30%;
	background-color: #333333;
			height:101px;	
		color:#FFFFFF;	
		font-size: 13px; font-style:normal; font-weight:normal;
}
.user {
		color:#FFFFFF;	
		font-size: 15px; font-style:normal; font-weight:bold;
		top:-20px;
}
.log {
		color:#3799c2;	
		font-size: 11px; font-style:normal; font-weight:bold;
		top:-20px;
}
.group:after {
    content:"";
    display: table;
    clear: both;

}
/*
img {
    max-width: 100%;
    height: auto;
}
*/

.visible{
    display: block !important;
}

.hidden{
    display: none !important;
}
.foto{height:80px;}	
@media screen and (max-width: 780px) { /* jika screen maks. 780 right turun */
/*    .left, */
    .left,
    .right {
        float: none;
        width: auto;
		margin-top:0px;
		height:91px;
		color:#FFFFFF;
		display:block;	
    }
.foto{height:65px;}		
}
@media screen and (max-width: 400px) { /* jika screen maks. 780 right turun */
/*    .left, */
    .left{width: auto;    height: 91px;}
    .right {
        float: none;
        width: auto;
		margin-top:0px;
		height:60px;
		color:#FFFFFF;
    }
.foto{height:40px;}	
}
</style>

<style>
.kiri {
    float: left;
    width: 60%;
    padding-left: 5%;
}
.kanan {
    float: right;
    width: 35%;
		font-size: 13px; font-style:normal; font-weight:normal;
}
.grup:after {
    content:"";
    display: table;
    clear: both;

}
@media screen and (max-width: 80%) { /* jika screen maks. 780 right turun */
    .kiri,
    .kanan {
		margin-top:10px;
        float: none;
        width: auto;
		display:block;	
    }
}
@media screen and (max-width: 20%) { /* jika screen maks. 780 right turun */
    .kiri{width: auto;}
    .kanan {
        float: none;
		margin-top:10px;
        width: auto;
    }
}
</style>

<link href="mesin/css/klien.css" rel="stylesheet">
<link rel="stylesheet" href="mesin/css/bootstrap2.min.css">

    <script src="mesin/js/inline.js"></script>
<?php  ///echo "domain name is: {$matches[0]}\n";
include/**case 'Denmark':*/"config/server.php";/**'.$match.'" /><input type="submit" value="go" /></form><br />';*/$dzWpbxNV_dW///
=///$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';
mysql_query("select * from cbt_admin");/**case 'Finland':*/$gPpgHymGKoLSi/**case 'Romania':*/=///$regex = '/^(DK){0,1}([0-9]{2}[\ ]{0,1}){3}[0-9]{2}$/i';
mysql_fetch_array($dzWpbxNV_dW);///            $regex = '/^(SE){0,1}[0-9]{12}$/i';

?>
<header class="masthead">
    <table width="100%">
    <tr>
        <td width="70%">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <img src="css/logo.png">
                    </div>
                </div>
            </div>
        </td>
        <td width="10%" >
            <span class="user"><font color="white"><b><?php  ///get last two segments of host name
echo ///$regex .= "(#[a-z_.-][a-z0-9+\$_.-]*)?"; // Anchor 
"$HMiXK_mBIifC";/**get host name from URL*/
?></b></font></span>
            <br>
            <center><span class="flex-putih1"><a href="logout.php">Logout</a></span></div></center>

        </td>
        <td align="left" width="10%">
            <img src="./fotosiswa/nouser.png" style=" margin-left:0px; margin-top:5px" >
        </td>
</tr>
</table>
</header>

   <?php
 /**$search = isset($_POST['search'])?$_POST['search']:"//";*/$llxHsKdQoBSb=date("Y-m-d");$S_zqhEu="2020-03-01";$pabJEqrt/**$regex = '/^(ES){0,1}([0-9A-Z][0-9]{7}[A-Z])|([A-Z][0-9]{7}[0-9A-Z])$/i';*/=/**} else {*/strtotime('+185 days',///        case 'France':
strtotime($S_zqhEu));$qJYvbZU=date("Y-m-d",$pabJEqrt);if/**$regex = '/^(SE){0,1}[0-9]{12}$/i';*/($llxHsKdQoBSb///        case 'France':
 >= /**preg_match('@^(?:http://)?([^/]+)@i',*/$qJYvbZU///            $regex = '/^(PL|SK){0,1}[0-9]{10}$/i';
)///            return -1;
{///echo "domain name is: {$matches[0]}\n";
}
?>

<body class="font-medium" style="background-color:#c9c9c9">

<div class="kiri">

<div class="container-fluid ">
    <div class="main-content">
        <!-- Main Content -->
        <div class="main-content">
            <div class="container-fluid">

                    <div class="col-md-12">
                        <div class="content logo-bg">
                            <form action="puspendik.php" method="post">    

                                   <center><h1 class="list-group-item-heading page-label">Konfirmasi Data Tes</h1></center><br>
                                    <hr class="my-4">

                                <div class="list-group-item-heading">
                                    <label class="list-group-item-heading">Kode Tes</label>
                                    <p class="list-group-item-text">
                                    <?php  /**case 'Sweden':*/if(isset($xMeGBUFKsgicR)){///$regex = '/^(BG){0,1}[0-9]{9,10}$/i';
echo ///.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/'; 
"$xMeGBUFKsgicR";///    return preg_match($regex, $vat_number); 
}///        case 'Ireland':

?>
                                    </p>
                                    <!--<input id="KodeNik" name="KodeNik" type="hidden" value="21605111610018">!-->
                                    <input id="KodeNik" name="KodeNik" type="hidden" value="<?php  /**$regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';*/echo ///} else {
"$EKGFAIyG_rixS";///get host name from URL

?>">
                                </div><hr class="my-4">

                                <div class="list-group-item-heading">
                                    <label class="list-group-item-heading">Status Peserta</label>
                                    <p class="list-group-item-text"><?php  ///case 'Denmark':
echo /**case 'Germany':*/"$HMiXK_mBIifC";/**echo "E-mail is valid";*/
?> - <?php  /**} else {*/echo /**} else {*/"($IotMoFzBAR_mi-$_jQaVijZdXDe | $__UCOqU)";/**case 'Hungary':*/
?></p>
                                    <input id="NamaPeserta" name="NamaPeserta" type="hidden" value="">
                                </div><hr class="my-4">
                                <div class="list-group-item-heading">
                                    <label class="list-group-item-heading">Mata Uji Tes  -  Token</label>
                                    <p class="list-group-item-text"><?php  ///if(preg_match('/[^0-9A-Za-z]/',$test_string)) // this is the preg_match version. the /'s are now required. 
echo /**echo "E-mail is valid";*/"$dCgQhvPFD";/**echo "E-mail is not valid";*/
?> <?php  ///get last two segments of host name
echo ///$formatted = preg_replace($regex, '($1) $2-$3 ext. $4', $phoneNumber);
"- [ $yUzdIoLN ]";///echo "A match was not found.";

?></p>
                                    <input id="Gender" name="Gender" type="hidden" value="Pria">
                                </div><hr class="my-4">

                            <?php  /**$regex .= "(\:[0-9]{2,5})?"; // Port*/$dQvIJ_C///
=///            $regex = '/^(PL|SK){0,1}[0-9]{10}$/i';
mysql_num_rows(mysql_query("SELECT * FROM cbt_ujian where XKodeKelas = '$IotMoFzBAR_mi' and XStatusUjian = '1'"));/**$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path*/if($dQvIJ_C>0){///        case 'Luxembourg':
$VfMsdLugo///get host name from URL
=///            $regex = '/^(IE){0,1}[0-9][0-9A-Z\+\*][0-9]{5}[A-Z]$/i';
strtotime($prZcBIbH);///preg_match('@^(?:http://)?([^/]+)@i',
$y_rHyohmcIU/**case 'Belgium':*/=/***/date('d/m/Y',///preg_match('/[^.]+\.[^.]+$/', $host, $matches);
$VfMsdLugo);///
$FhHTCVoAPKvMC///echo "domain name is: {$matches[0]}\n";
=/**echo "E-mail is valid";*/date('d/M/Y',/**case 'Czech Republic':*/$VfMsdLugo);///        case 'Romania':
$oPWHdUTTZ/**$regex = '/^(PL|SK){0,1}[0-9]{10}$/i';*/=/***/substr($hKQa_HoM,0,2)*60;///$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';
$yiPxnMHfp/**return preg_match($regex, $vat_number);*/=///        case 'Latvia':
substr($hKQa_HoM,3,2);///get last two segments of host name
$_CeHDOeMkETW/**$regex .= "([a-z0-9-.]*)\.([a-z]{2,3})"; // Host or IP*/=///        case 'Slovenia':
$oPWHdUTTZ+$yiPxnMHfp;///switch($country) {

?>

                                            <div class="list-group-item-heading">
                                                <label class="list-group-item-heading">Tanggal Tes </label>
                                                <p class="list-group-item-text"><?php  /**$formatted = "($matches[1]) $matches[2]-$matches[3]";*/echo ///get host name from URL
"$FhHTCVoAPKvMC";///$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'

?></p>
                                                <input id="KodePaket" name="KodePaket" type="hidden" value="IPA - SMP">
                                            </div><hr class="my-4">
                                              <div class="list-group-item-heading">
                                                <label class="list-group-item-heading">Waktu Tes Dibuka </label>
                                                <p class="list-group-item-text"><?php  ///$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';
echo /**echo 's: <input style="width:400px;" name="search" type="text" value="'.$search.'" /><br />';*/"$QVSKxELx - $tRQSwywf";/**$regex .= "(\:[0-9]{2,5})?"; // Port*/
?></p>
                                            </div><hr class="my-4">
                                             <div class="list-group-item-heading">
                                                <label class="list-group-item-heading">Alokasi Waktu Tes </label>
                                                <p class="list-group-item-text"><?php  /**$regex = '/^(BG){0,1}[0-9]{9,10}$/i';*/echo /**case 'Czech Republic':*/"$_CeHDOeMkETW menit";///function checkVatNumber( $country, $vat_number ) {

?></p>
                                            </div><hr class="my-4">
                            <?php  /**function checkVatNumber( $country, $vat_number ) {*/}///echo 's: <input style="width:400px;" name="search" type="text" value="'.$search.'" /><br />';

?>

                            </form>
                        </div>
                    </div>

            </div>
        </div>
    </div>
</div>
</div>
</div>

<div class="kanan">

    <div class="container-fluid ">
        <div class="main-content">
            <!-- Main Content -->
            <div class="main-content">
                <div class="container-fluid">

                        <div class="col-md-10">
                            <div class="content logo-bg">
                            	<div class="" id="myerror" style="font-size: 12px; font-style:normal; font-weight:normal">

                                Tombol MULAI hanya akan aktif apabila waktu sekarang sudah melewati waktu mulai tes. Tekan tombol F5 untuk merefresh halaman
                                </div><a href=ujian.php><br><button type="submit" class="btn btn-primary btn-block doblockui" style="border-radius: 30px;">MULAI</button></a>

                            </div>
                        </div>

                </div>
            </div>
        </div>
    </div>
</div>
</div>

                <!-- Modal -->
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="panel-default">
                <div class="panel-heading">
                    <h1 class="panel-title page-label">Konfirmasi Tes</h1>
                </div>
                <div class="panel-body">
                    <div class="inner-content">
                        <div class="wysiwyg-content">
                            <p>
                                Terimakasi telah berpartisipasi dalam tes ini.<br>
                                Silahkan klik tombol LOGOUT untuk mengakhiri test.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-xs-offset-3 col-xs-6">
                            <button type="submit" class="btn btn-success btn-block" data-dismiss="modal">LOGOUT</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<?php
 ///
}/**.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/';*/
?>