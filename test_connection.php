<?php
// Simple connection test to verify the fix
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Connection Test</h2>";

try {
    include "config/server.php";
    echo "<p>✅ Server.php included successfully</p>";
    echo "<p>✅ No function redeclaration errors</p>";
    
    if (isset($database)) {
        echo "<p>✅ Database object created</p>";
        
        // Test basic query
        $test = mysql_query("SELECT 1 as test");
        if ($test) {
            echo "<p>✅ Database query successful</p>";
        } else {
            echo "<p>❌ Database query failed: " . mysql_error() . "</p>";
        }
    } else {
        echo "<p>❌ Database object not created</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='panel/'>Go to Panel</a></p>";
echo "<p><a href='create_missing_tables.php'>Create Missing Tables</a></p>";
?>
