<?php
// Comprehensive connection test to verify all fixes
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Comprehensive Connection Test</h2>";

try {
    include "config/server.php";
    echo "<p>✅ Server.php included successfully</p>";
    echo "<p>✅ No function redeclaration errors</p>";

    if (isset($database)) {
        echo "<p>✅ Database object created</p>";

        // Test basic query
        $test = mysql_query("SELECT 1 as test");
        if ($test) {
            echo "<p>✅ mysql_query() working</p>";
        } else {
            echo "<p>❌ mysql_query() failed: " . mysql_error() . "</p>";
        }

        // Test mysql_get_server_info()
        $server_info = mysql_get_server_info();
        echo "<p>✅ mysql_get_server_info(): " . $server_info . "</p>";

        // Test mysql_affected_rows()
        $affected = mysql_affected_rows();
        echo "<p>✅ mysql_affected_rows(): " . $affected . "</p>";

        // Test mysql_errno()
        $errno = mysql_errno();
        echo "<p>✅ mysql_errno(): " . $errno . "</p>";

        echo "<h3>All MySQL compatibility functions are working!</h3>";

    } else {
        echo "<p>❌ Database object not created</p>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Next Steps:</h3>";
echo "<p><a href='create_missing_tables.php' class='btn btn-primary'>Create Missing Tables</a></p>";
echo "<p><a href='panel/' class='btn btn-success'>Go to Panel</a></p>";
echo "<p><a href='check_database.php' class='btn btn-info'>Check Database</a></p>";

echo "<style>
.btn {
    display: inline-block;
    padding: 10px 15px;
    margin: 5px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
}
.btn-success { background: #28a745; }
.btn-info { background: #17a2b8; }
.btn-primary { background: #007bff; }
</style>";
?>
