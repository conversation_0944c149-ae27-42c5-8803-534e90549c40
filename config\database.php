<?php
/**
 * Modern Database Connection Class for PHP 8
 * Replaces deprecated mysql_* functions with MySQLi
 * 
 * This class provides a modern, secure database connection layer
 * compatible with PHP 8 and maintains backward compatibility with
 * the existing CBT application structure.
 */

class Database {
    private $connection;
    private $host;
    private $username;
    private $password;
    private $database;
    private static $instance = null;
    
    /**
     * Constructor - Initialize database connection parameters
     */
    private function __construct($host = 'localhost:3306', $username = 'root', $password = '', $database = 'cbt') {
        $this->host = $host;
        $this->username = $username;
        $this->password = $password;
        $this->database = $database;
        $this->connect();
    }
    
    /**
     * Singleton pattern to ensure single database connection
     */
    public static function getInstance($host = 'localhost:3306', $username = 'root', $password = '', $database = 'cbt') {
        if (self::$instance === null) {
            self::$instance = new self($host, $username, $password, $database);
        }
        return self::$instance;
    }
    
    /**
     * Establish database connection using MySQLi
     */
    private function connect() {
        // Handle port in hostname
        $host_parts = explode(':', $this->host);
        $host = $host_parts[0];
        $port = isset($host_parts[1]) ? (int)$host_parts[1] : 3306;

        // First try to connect without selecting database
        $this->connection = new mysqli($host, $this->username, $this->password, '', $port);

        if ($this->connection->connect_error) {
            error_log("Database connection failed: " . $this->connection->connect_error);
            die('Connection failed: ' . $this->connection->connect_error);
        }

        // Set charset to UTF-8 for proper character handling
        $this->connection->set_charset('utf8');

        // Try to select the database, create if it doesn't exist
        if (!$this->connection->select_db($this->database)) {
            // Try to create the database
            $create_db_sql = "CREATE DATABASE IF NOT EXISTS `" . $this->database . "` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci";
            if ($this->connection->query($create_db_sql)) {
                $this->connection->select_db($this->database);
                error_log("Database '{$this->database}' created successfully");
            } else {
                error_log("Failed to create database '{$this->database}': " . $this->connection->error);
                die("Database '{$this->database}' does not exist and could not be created");
            }
        }
    }
    
    /**
     * Get the MySQLi connection object
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * Execute a query - replacement for mysql_query()
     */
    public function query($sql) {
        $result = $this->connection->query($sql);
        if ($result === false) {
            // Check if it's a "table doesn't exist" error
            if (strpos($this->connection->error, "doesn't exist") !== false) {
                error_log("Database table missing: " . $this->connection->error . " | Query: " . $sql);
                // Return false for missing tables instead of throwing fatal error
                return false;
            } else {
                error_log("Database query error: " . $this->connection->error . " | Query: " . $sql);
            }
        }
        return $result;
    }
    
    /**
     * Fetch associative array - replacement for mysql_fetch_array()
     */
    public function fetchArray($result) {
        if ($result && $result instanceof mysqli_result) {
            return $result->fetch_assoc();
        }
        return false;
    }
    
    /**
     * Get number of rows - replacement for mysql_num_rows()
     */
    public function numRows($result) {
        if ($result && $result instanceof mysqli_result) {
            return $result->num_rows;
        }
        return 0;
    }
    
    /**
     * Get last error - replacement for mysql_error()
     */
    public function error() {
        return $this->connection->error;
    }
    
    /**
     * Get last insert ID
     */
    public function insertId() {
        return $this->connection->insert_id;
    }
    
    /**
     * Escape string for safe SQL queries
     */
    public function escapeString($string) {
        return $this->connection->real_escape_string($string);
    }
    
    /**
     * Select database - replacement for mysql_select_db()
     */
    public function selectDb($database) {
        $this->database = $database;
        return $this->connection->select_db($database);
    }
    
    /**
     * Close connection - replacement for mysql_close()
     */
    public function close() {
        if ($this->connection) {
            $this->connection->close();
        }
    }
    
    /**
     * Prepared statement for secure queries
     */
    public function prepare($sql) {
        return $this->connection->prepare($sql);
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->connection->autocommit(false);
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        $result = $this->connection->commit();
        $this->connection->autocommit(true);
        return $result;
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        $result = $this->connection->rollback();
        $this->connection->autocommit(true);
        return $result;
    }
    
    /**
     * Destructor - ensure connection is closed
     */
    public function __destruct() {
        $this->close();
    }
}

/**
 * Global helper functions to maintain backward compatibility
 * These functions wrap the Database class methods to provide
 * drop-in replacements for deprecated mysql_* functions
 */

// Global database instance
$GLOBALS['db_instance'] = null;

/**
 * Initialize database connection - replacement for mysql_connect()
 */
function db_connect($host = 'localhost:3306', $username = 'root', $password = '') {
    $GLOBALS['db_instance'] = Database::getInstance($host, $username, $password);
    return $GLOBALS['db_instance'] ? true : false;
}

/**
 * Select database - replacement for mysql_select_db()
 */
function db_select_db($database, $connection = null) {
    if (!$GLOBALS['db_instance']) {
        $GLOBALS['db_instance'] = Database::getInstance();
    }
    return $GLOBALS['db_instance']->selectDb($database);
}

/**
 * Execute query - replacement for mysql_query()
 */
function db_query($sql, $connection = null) {
    if (!$GLOBALS['db_instance']) {
        $GLOBALS['db_instance'] = Database::getInstance();
    }
    return $GLOBALS['db_instance']->query($sql);
}

/**
 * Fetch array - replacement for mysql_fetch_array()
 */
function db_fetch_array($result) {
    if (!$GLOBALS['db_instance']) {
        $GLOBALS['db_instance'] = Database::getInstance();
    }
    return $GLOBALS['db_instance']->fetchArray($result);
}

/**
 * Get number of rows - replacement for mysql_num_rows()
 */
function db_num_rows($result) {
    if (!$GLOBALS['db_instance']) {
        $GLOBALS['db_instance'] = Database::getInstance();
    }
    return $GLOBALS['db_instance']->numRows($result);
}

/**
 * Get error - replacement for mysql_error()
 */
function db_error() {
    if (!$GLOBALS['db_instance']) {
        $GLOBALS['db_instance'] = Database::getInstance();
    }
    return $GLOBALS['db_instance']->error();
}

/**
 * Escape string
 */
function db_escape_string($string) {
    if (!$GLOBALS['db_instance']) {
        $GLOBALS['db_instance'] = Database::getInstance();
    }
    return $GLOBALS['db_instance']->escapeString($string);
}
?>
