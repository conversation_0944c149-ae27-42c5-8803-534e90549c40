[PHP]


engine=On

short_open_tag=Off

asp_tags=Off

precision=14

output_buffering=4096

zlib.output_compression=Off

implicit_flush=Off

unserialize_callback_func=

serialize_precision=17

disable_functions=

disable_classes=

zend.enable_gc=On

expose_php=On

max_execution_time=5000

max_input_time=360

memory_limit=1024M

error_reporting=E_ALL & ~E_DEPRECATED & ~E_STRICT

display_errors=On

display_startup_errors=On

log_errors=On

log_errors_max_len=1024

ignore_repeated_errors=Off

ignore_repeated_source=Off

report_memleaks=On

track_errors=On

html_errors=On

variables_order="GPCS"

request_order="GP"

register_argc_argv=Off

auto_globals_jit=On

post_max_size=100000M

auto_prepend_file=

auto_append_file=

default_mimetype="text/html"

default_charset="UTF-8"

include_path=C:\xampp\php\PEAR

doc_root=

user_dir=

extension_dir="C:\xampp\php\ext"

enable_dl=Off

file_uploads=On

upload_tmp_dir="C:\xampp\tmp"

upload_max_filesize=3000M

post_max_size=3000M

max_file_uploads=1000

allow_url_fopen=On

allow_url_include=Off

default_socket_timeout=0

extension=php_bz2.dll
extension=php_curl.dll
extension=php_fileinfo.dll
extension=php_gd2.dll
extension=php_gettext.dll

extension=php_mbstring.dll
extension=php_exif.dll      ; Must be after mbstring as it depends on it
;extension=php_mysql.dll    ; REMOVED: mysql extension deprecated in PHP 5.5.0 and removed in PHP 7.0.0
extension=php_mysqli.dll

extension=php_pdo_mysql.dll

extension=php_pdo_sqlite.dll

asp_tags=Off
display_startup_errors=On
;track_errors=Off                    ; REMOVED: track_errors deprecated in PHP 7.2.0 and removed in PHP 8.0.0
;y2k_compliance=On                   ; REMOVED: y2k_compliance removed in PHP 5.4.0
;allow_call_time_pass_reference=Off  ; REMOVED: allow_call_time_pass_reference removed in PHP 5.4.0
;safe_mode=Off                       ; REMOVED: safe_mode removed in PHP 5.4.0
;safe_mode_gid=Off                   ; REMOVED: safe_mode_gid removed in PHP 5.4.0
;safe_mode_allowed_env_vars=PHP_     ; REMOVED: safe_mode_allowed_env_vars removed in PHP 5.4.0
;safe_mode_protected_env_vars=LD_LIBRARY_PATH ; REMOVED: safe_mode_protected_env_vars removed in PHP 5.4.0
error_log="C:\xampp\php\logs\php_error_log"
;register_globals=Off                ; REMOVED: register_globals removed in PHP 5.4.0
;register_long_arrays=Off            ; REMOVED: register_long_arrays removed in PHP 5.3.0
;magic_quotes_gpc=Off                ; REMOVED: magic_quotes_gpc removed in PHP 5.4.0
;magic_quotes_runtime=Off            ; REMOVED: magic_quotes_runtime removed in PHP 5.4.0
;magic_quotes_sybase=Off             ; REMOVED: magic_quotes_sybase removed in PHP 5.4.0
extension=php_openssl.dll

[CLI Server]

cli_server.color=On

[Date]


[filter]


[iconv]


[intl]


[sqlite3]


[Pcre]

[Pdo]
pdo_mysql.default_socket="MySQL"


[Pdo_mysql]

pdo_mysql.cache_size=2000

pdo_mysql.default_socket=

[Phar]


[mail function]

SMTP=localhost

smtp_port=25

mail.add_x_header=On

[SQL]
sql.safe_mode=Off

[ODBC]

odbc.allow_persistent=On

odbc.check_persistent=On

odbc.max_persistent=-1

odbc.max_links=-1

odbc.defaultlrl=4096

odbc.defaultbinmode=1


[Interbase]

ibase.allow_persistent=1

ibase.max_persistent=-1

ibase.max_links=-1

ibase.timestampformat="%Y-%m-%d %H:%M:%S"

ibase.dateformat="%Y-%m-%d"

ibase.timeformat="%H:%M:%S"

[MySQL]

mysql.allow_local_infile=On

mysql.allow_persistent=On

mysql.cache_size=2000

mysql.max_persistent=-1

mysql.max_links=-1

mysql.default_port=

mysql.default_socket=

mysql.default_host=

mysql.default_user=

mysql.default_password=

mysql.connect_timeout=60

mysql.trace_mode=Off

[MySQLi]

mysqli.max_persistent=-1

mysqli.allow_persistent=On

mysqli.max_links=-1

mysqli.cache_size=2000

mysqli.default_port=3306

mysqli.default_socket=

mysqli.default_host=

mysqli.default_user=

mysqli.default_pw=

mysqli.reconnect=Off

[mysqlnd]

mysqlnd.collect_statistics=On

mysqlnd.collect_memory_statistics=On


[OCI8]


[PostgreSQL]

pgsql.allow_persistent=On

pgsql.auto_reset_persistent=Off

pgsql.max_persistent=-1

pgsql.max_links=-1

pgsql.ignore_notice=0

pgsql.log_notice=0

[Sybase-CT]

sybct.allow_persistent=On

sybct.max_persistent=-1

sybct.max_links=-1

sybct.min_server_severity=10

sybct.min_client_severity=10


[bcmath]

bcmath.scale=0

[browscap]

browscap="C:\xampp\php\extras\browscap.ini"

[Session]

session.save_handler=files

session.save_path="C:\xampp\tmp"

session.use_strict_mode=0

session.use_cookies=1

session.use_only_cookies=1

session.name=PHPSESSID

session.auto_start=0

session.cookie_lifetime=0

session.cookie_path=/

session.cookie_domain=

session.cookie_httponly=

session.serialize_handler=php

session.gc_probability=1

session.gc_divisor=1000

session.gc_maxlifetime=1440

session.referer_check=

session.entropy_length=0

session.cache_limiter=nocache

session.cache_expire=180

session.use_trans_sid=0

session.hash_function=0

session.hash_bits_per_character=5

url_rewriter.tags="a=href,area=href,frame=src,input=src,form=fakeentry"


[MSSQL]

mssql.allow_persistent=On

mssql.max_persistent=-1

mssql.max_links=-1

mssql.min_error_severity=10

mssql.min_message_severity=10

mssql.compatibility_mode=Off

mssql.secure_connection=Off


[Assertion]


[COM]


[mbstring]


[gd]


[exif]


[Tidy]

tidy.clean_output=Off

[soap]

soap.wsdl_cache_enabled=1

soap.wsdl_cache_dir="/tmp"

soap.wsdl_cache_ttl=86400

soap.wsdl_cache_limit=5

[sysvshm]

[ldap]

ldap.max_links=-1

[mcrypt]


[dba]

[opcache]


[curl]


[openssl]

[Syslog]
define_syslog_variables=Off
[Session]
define_syslog_variables=Off
[Date]
date.timezone=Europe/Berlin
[MySQL]
mysql.allow_local_infile=On
mysql.allow_persistent=On
mysql.cache_size=2000
mysql.max_persistent=-1
mysql.max_link=-1
mysql.default_port=3306
mysql.default_socket="MySQL"
mysql.connect_timeout=3
mysql.trace_mode=Off
[Sybase-CT]
sybct.allow_persistent=On
sybct.max_persistent=-1
sybct.max_links=-1
sybct.min_server_severity=10
sybct.min_client_severity=10
[MSSQL]
mssql.allow_persistent=On
mssql.max_persistent=-1
mssql.max_links=-1
mssql.min_error_severity=10
mssql.min_message_severity=10
mssql.compatability_mode=Off
mssql.secure_connection=Off
