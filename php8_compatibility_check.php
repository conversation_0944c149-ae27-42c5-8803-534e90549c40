<?php
// PHP 8 Compatibility Check Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>PHP 8 Compatibility Check</h2>";
echo "<p>Checking for common PHP 8 compatibility issues...</p>";

$issues_found = 0;

// Check 1: Invalid numeric literals (like 08, 09)
echo "<h3>1. Checking for Invalid Numeric Literals</h3>";
$files_to_check = [
    'panel/pages/berita_acara.php',
    'panel/pages/daftar_tesbaru.php', 
    'panel/pages/daftar_tes.php',
    'panel/pages/daftar_waktu.php',
    'panel/pages/daftar_waktu_db.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (preg_match('/\$\w+\s*=\s*0[89]/', $content)) {
            echo "<p>❌ Found invalid numeric literal in: $file</p>";
            $issues_found++;
        } else {
            echo "<p>✅ $file - No invalid numeric literals found</p>";
        }
    } else {
        echo "<p>⚠️ File not found: $file</p>";
    }
}

// Check 2: MySQL functions availability
echo "<h3>2. Checking MySQL Compatibility Functions</h3>";
$mysql_functions = [
    'mysql_connect', 'mysql_query', 'mysql_fetch_array', 'mysql_num_rows',
    'mysql_error', 'mysql_close', 'mysql_real_escape_string', 'mysql_insert_id',
    'mysql_get_server_info', 'mysql_affected_rows', 'mysql_errno'
];

foreach ($mysql_functions as $func) {
    if (function_exists($func)) {
        echo "<p>✅ $func() - Available</p>";
    } else {
        echo "<p>❌ $func() - Not available</p>";
        $issues_found++;
    }
}

// Check 3: Database connection test
echo "<h3>3. Testing Database Connection</h3>";
try {
    include "config/server.php";
    echo "<p>✅ Database connection established</p>";
    
    $test_query = mysql_query("SELECT 1 as test");
    if ($test_query) {
        echo "<p>✅ Database queries working</p>";
    } else {
        echo "<p>❌ Database query failed: " . mysql_error() . "</p>";
        $issues_found++;
    }
} catch (Exception $e) {
    echo "<p>❌ Database connection error: " . $e->getMessage() . "</p>";
    $issues_found++;
}

// Check 4: Required tables
echo "<h3>4. Checking Required Database Tables</h3>";
$required_tables = ['cbt_admin', 'cbt_user', 'cbt_siswa', 'cbt_ujian', 'cbt_soal', 'cbt_sync', 'cbt_header'];

foreach ($required_tables as $table) {
    $check = mysql_query("SHOW TABLES LIKE '$table'");
    if ($check && mysql_num_rows($check) > 0) {
        echo "<p>✅ Table '$table' exists</p>";
    } else {
        echo "<p>❌ Table '$table' missing</p>";
        $issues_found++;
    }
}

// Check 5: PHP version
echo "<h3>5. PHP Version Check</h3>";
$php_version = phpversion();
echo "<p>Current PHP Version: $php_version</p>";

if (version_compare($php_version, '8.0.0', '>=')) {
    echo "<p>✅ PHP 8.0+ detected</p>";
} else {
    echo "<p>⚠️ PHP version is below 8.0</p>";
}

// Summary
echo "<h3>Summary</h3>";
if ($issues_found == 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ All Checks Passed!</h4>";
    echo "<p>Your CBT application appears to be fully compatible with PHP 8.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='panel/'>Access the Panel</a></li>";
    echo "<li><a href='login.php'>Test Student Login</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Issues Found: $issues_found</h4>";
    echo "<p>Please address the issues above before proceeding.</p>";
    echo "<p><strong>Recommended actions:</strong></p>";
    echo "<ul>";
    if ($issues_found > 0) {
        echo "<li><a href='create_missing_tables.php'>Create Missing Tables</a></li>";
        echo "<li><a href='init_database.php'>Initialize Database</a></li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h3>Additional Tools</h3>";
echo "<p><a href='test_connection.php'>Test Database Connection</a></p>";
echo "<p><a href='check_database.php'>Detailed Database Check</a></p>";
echo "<p><a href='create_missing_tables.php'>Create Missing Tables</a></p>";
echo "<p><a href='init_database.php'>Initialize Database</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
