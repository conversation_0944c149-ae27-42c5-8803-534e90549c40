<?php
// Simple test file to check if PHP is working in panel directory
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Panel Directory Test</h1>";
echo "<p>PHP is working in the panel directory!</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>Testing Database Connection</h2>";
try {
    include "../config/server.php";
    echo "<p>✅ Server.php included successfully</p>";
    
    if (isset($database)) {
        echo "<p>✅ Database object exists</p>";
        
        // Test a simple query
        $test_query = mysql_query("SELECT 1 as test");
        if ($test_query) {
            echo "<p>✅ Database query successful</p>";
        } else {
            echo "<p>❌ Database query failed: " . mysql_error() . "</p>";
        }
        
        // Check if cbt_admin table exists
        $table_check = mysql_query("SHOW TABLES LIKE 'cbt_admin'");
        if ($table_check && mysql_num_rows($table_check) > 0) {
            echo "<p>✅ cbt_admin table exists</p>";
            
            $admin_check = mysql_query("SELECT COUNT(*) as count FROM cbt_admin");
            if ($admin_check) {
                $count = mysql_fetch_array($admin_check);
                echo "<p>Records in cbt_admin: " . $count['count'] . "</p>";
                
                if ($count['count'] > 0) {
                    echo "<p>✅ Admin data available</p>";
                    if (isset($log) && is_array($log)) {
                        echo "<p>✅ \$log variable is set</p>";
                        echo "<p>School: " . (isset($log['XSekolah']) ? $log['XSekolah'] : 'Not set') . "</p>";
                    } else {
                        echo "<p>❌ \$log variable is not set or not an array</p>";
                    }
                } else {
                    echo "<p>❌ No admin data found</p>";
                }
            }
        } else {
            echo "<p>❌ cbt_admin table does not exist</p>";
        }
        
    } else {
        echo "<p>❌ Database object not created</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If database tables don't exist, you need to:</p>";
echo "<ol>";
echo "<li>Import the database structure from config/cbt.sql</li>";
echo "<li>Or use the database creation tool</li>";
echo "</ol>";

echo "<p><a href='../check_database.php'>Run Database Check</a></p>";
echo "<p><a href='index.php'>Try Panel Index Again</a></p>";
?>
