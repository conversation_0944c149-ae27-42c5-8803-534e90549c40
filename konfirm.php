<?php
// Clean student login confirmation - PHP 8 compatible
// Trial period restrictions permanently removed
include "config/server.php";
include "ip.php";

// Check database connection
$db_check = mysql_query("SELECT * FROM `cbt_siswa` LIMIT 1");
if (!$db_check) {
    header('Location:login.php?salah=2');
    exit;
}

// Get login credentials
if (isset($_COOKIE['PESERTA']) && isset($_COOKIE['KUNCI'])) {
    $student_id = $_COOKIE['PESERTA'];
    $password = $_COOKIE['KUNCI'];
} else {
    $student_id = trim($_REQUEST['UserName']);
    $password = trim($_REQUEST['Password']);
    setcookie('PESERTA', $student_id);
    setcookie('KUNCI', $password);
}

// Sanitize input to prevent SQL injection
$student_id = mysql_real_escape_string($student_id);
$password = mysql_real_escape_string($password);

// Verify student credentials
$student_query = mysql_query("SELECT * FROM `cbt_siswa` WHERE XNomerUjian = '$student_id' AND XPassword = '$password'");
$student_data = mysql_fetch_array($student_query);

if (!$student_data) {
    $num_rows = mysql_num_rows($student_query);
    header('Location:login.php?salah=1&jumlah=' . $num_rows);
    exit;
}

// Extract student information
$student_name = $student_data['XNamaSiswa'];
$gender = $student_data['XJenisKelamin'];
$class_code = $student_data['XKodeKelas'];
$major_code = $student_data['XKodeJurusan'];
$class_name = $student_data['XNamaKelas'];
$session = $student_data['XSesi'];
$photo = $student_data['XFoto'];

// Set default photo if empty
if (empty($photo)) {
    $photo = "avatar.gif";
}

// Set gender display
$gender_display = ($gender == "L") ? "LAKI-LAKI" : "PEREMPUAN";

// Get current date and time
$current_date = date("Y-m-d");
$current_time = date("H:i:s");
// Get available exams for the student
$exam_query = mysql_query("
    SELECT u.*, m.XNamaMapel
    FROM `cbt_ujian` u
    LEFT JOIN cbt_paketsoal p ON p.XKodeKelas = u.XKodeKelas AND p.XKodeMapel = u.XKodeMapel
    LEFT JOIN cbt_mapel m ON u.XKodeMapel = m.XKodeMapel
    WHERE (u.XKodeKelas = '$class_code' OR u.XKodeKelas = 'ALL')
    AND (u.XKodeJurusan = '$major_code' OR u.XKodeJurusan = 'ALL')
    AND u.XTglUjian = '$current_date'
    AND u.XJamUjian <= '$current_time'
    AND u.XStatusUjian = '1'
");

$exam_data = mysql_fetch_array($exam_query);

if (!$exam_data) {
    // No active exams found
    header('Location:login.php?salah=4');
    exit;
}

// Extract exam information
$exam_code = $exam_data['XKodeSoal'];
$exam_class = $exam_data['XKodeKelas'];
$exam_major = $exam_data['XKodeJurusan'];
$exam_date = $exam_data['XTglUjian'];
$subject_code = $exam_data['XKodeMapel'];
$total_questions = $exam_data['XJumSoal'];
$exam_token = $exam_data['XTokenUjian'];
$exam_duration = $exam_data['XLamaUjian'];
$exam_time = $exam_data['XJamUjian'];
$entry_limit = $exam_data['XBatasMasuk'];
$subject_name = $exam_data['XNamaMapel'];
$token_status = $exam_data['XStatusToken'];

// Check if student has already taken this exam
$student_exam_query = mysql_query("SELECT * FROM `cbt_siswa_ujian` WHERE XNomerUjian = '$student_id' AND XTokenUjian = '$exam_token'");
$student_exam_data = mysql_fetch_array($student_exam_query);

// IP address validation (if required)
$student_ip = trim($student_exam_data['XGetIP']);
$current_ip = $student_ip;

if (!empty($student_ip) && $current_ip != $student_ip) {
    header('Location:login.php?salah=3');
    exit;
}
?>

<!DOCTYPE html>
<html class="no-js" lang="en">

<script type="text/javascript">
function mousedwn(e){try{if(event.button==2||event.button==3)return false}catch(e){if(e.which==3)return false}}document.oncontextmenu=function(){return false};document.ondragstart=function(){return false};document.onmousedown=mousedwn
</script>
<script type="text/javascript">
window.addEventListener("keydown",function(e){if(e.ctrlKey&&(e.which==65||e.which==66||e.which==67||e.which==73||e.which==80||e.which==83||e.which==85||e.which==86)){e.preventDefault()}});document.keypress=function(e){if(e.ctrlKey&&(e.which==65||e.which==66||e.which==67||e.which==73||e.which==80||e.which==83||e.which==85||e.which==86)){}return false}
</script>
<script type="text/javascript">
document.onkeydown=function(e){e=e||window.event;if(e.keyCode==123||e.keyCode==18){return false}}
</script>  

<head>
	<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>Aplikasi UNBK | Login Untuk Memulai Ujian</title>
		<!-- Tell the browser to be responsive to screen width -->
		<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
		<meta name="description" content="Aplikasi UNBK, membantu anda sukses dalam ujian dengan memulai belajar test berbasis Komputer dengan beragam soal-soal ujian."> 
		<meta name="keyword" content="UNBK, Ujian, Ujian Nasional, Ulangan Harian, Ulangan Semester, Mid Semester, Test CPNS, Test SMBPTN">
		<meta name="google" content="nositelinkssearchbox" />
		<meta name="robots" content="index, follow">

	    <link href="css/bootstrap.min.css" rel="stylesheet">
		<link href="css/main.css" rel="stylesheet">
		<link href="css/mainam.css" rel="stylesheet">
		<link href="css/mainan.css" rel="stylesheet">
		<link href="css/selectbox.min.css" rel="stylesheet">

		<!-- jQuery 3 -->
		<script src="css/jquery.min.js"></script>
	<script>
		function disableBackButton() {window.history.forward();}
		setTimeout("disableBackButton()", 0);
	</script>

	<link href="mesin/css/klien.css" rel="stylesheet">
	<link rel="stylesheet" href="mesin/css/bootstrap2.min.css">
    <script src="mesin/js/inline.js"></script>
	<?php  /**$regex = '/^(CZ){0,1}[0-9]{8,10}$/i';*/include///        case 'Lithuania':
"config/server.php";///        case 'United Kingdom':
$bCkQoLJVPy_J///
=///
mysql_query("select * from cbt_admin");///        case 'Spain':
$r_r_QlbvZbGSB///$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'
=///$regex = '/^(BG){0,1}[0-9]{9,10}$/i';
mysql_fetch_array($bCkQoLJVPy_J);///            $regex = '/^(FI|HU|LU|MT|SI){0,1}[0-9]{8}$/i';

?>

<body class="font-medium" style="background-color:#c9c9c9">
<header class="masthead">
    <table width="100%">
    <tr>
        <td width="70%">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <img src="css/logo.png">
                    </div>
                </div>
            </div>
        </td>
        <td width="10%" >
            <span class="user"><font color="white"><b><?php echo $student_name; ?></b></font></span>
            <br>
            <center><span class="flex-putih1"><a href="logout.php">Logout</a></span></div></center>

        </td>
        <td align="left" width="10%">
            <img src="./fotosiswa/nouser.png" style=" margin-left:0px; margin-top:5px" >
        </td>
</tr>
</table>
</header>

<!--
<ul>
  	<div id="myerror" class="alert alert-danger" role="alert" style=" font-size: 13px; font-style:normal; font-weight:normal; margin-left:-40px; padding-left:30px;">
		<?php  /***/if(isset($_REQUEST['salah'])){if($_REQUEST['salah']/**case 'Greece':*/ == /**echo 'm:<input style="width:400px;" name="match" type="text" value="*/1)/**case 'Romania':*/{echo ///if (preg_match("/php/i", "PHP is the web scripting language of choice."))
"<b><ul><li>Kode TOKEN Tidak sesuai</li></ul></b>";}/**case 'Netherlands':*/}/**$email = "someone@domain .local";*/
?>
	</div>
</ul>
    -->

   <?php
// Trial period restrictions permanently removed
// Application is now fully functional without time limitations
}
?>

<div class="grup" style="width:50%; margin:0 auto; margin-top:1px">
<div class="container-fluid ">
	<div class="main-content">
		<!-- Main Content -->
		<div class="main-content">
			<div class="container-fluid">
				<div class="row no-gutters" >
					<div class="col-md-12">
						<div class="content logo-bg">

							<form action="mulai.php" method="post">    

									<center><h1 class="list-group-item-heading page-label">Konfirmasi Data Peserta</h1></center><br>
							        <hr class="my-4">        
							                <div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Kode Peserta / User Name</label>
							                    <p class="list-group-item-text"><?php  /**$regex .= "(#[a-z_.-][a-z0-9+\$_.-]*)?"; // Anchor*/echo ///if ($matches[4]) $formatted .= " $matches[4]"; 
"$chRBeG_";///            $regex = '/^(LT){0,1}([0-9]{9}|[0-9]{12})$/i';

?></p>
							                    <!--<input id="KodeNik" name="KodeNik" type="hidden" value="<?php  /**$regex = '/^(DK){0,1}([0-9]{2}[\ ]{0,1}){3}[0-9]{2}$/i';*/echo ///echo "E-mail is valid";
"$chRBeG_";///echo 'm:<input style="width:400px;" name="match" type="text" value="

?>">!-->
							                    <input id="KodeNik" name="KodeNik" type="hidden" value="<?php  /**case 'Poland':*/echo /**case 'Ireland':*/"$chRBeG_";/**case 'Bulgaria':*/
?>">
							                </div><hr class="my-4">
							                <div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Status Peserta</label>
							                    <p class="list-group-item-text"><?php  ///case 'Austria':
echo ///$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path
"$WRwRIQk ($qWwOSto-$uPCriqjKR | $UA_IiUQgElRDF)";/**case 'Czech Republic':*/
?></p>
							                    <input id="NamaPeserta" name="NamaPeserta" type="hidden" value="glyphicon-warning-sign">
							                </div><hr class="my-4">
							                <div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Jenis Kelamin</label>
							                    <p class="list-group-item-text"><?php  ///        case 'Greece':
echo /**$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';*/"$_O__noYgeOkUx";///

?></p>
							                    <input id="Gender" name="Gender" type="hidden" value="Pria">
							                </div><hr class="my-4">
							<?php  /**case 'Italy':*/$__GytpDJogre///"http://www.php.net/index.html", $matches);
=///$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})'
mysql_query("SELECT * FROM  `cbt_siswa_ujian` WHERE XNomerUjian = '$Pn_VsDZO' and XTokenUjian = '$dAyPeD_hf'");/**$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})'*/$h_I_sEM/**$regex = '/^(DK){0,1}([0-9]{2}[\ ]{0,1}){3}[0-9]{2}$/i';*/=///echo '<form method="post">';
mysql_fetch_array($__GytpDJogre);/**$regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';*/$bqiAjd_pFhHgT/**echo "E-mail is not valid";*/=///get last two segments of host name
$h_I_sEM['XStatusUjian'];///echo "E-mail is valid";
$cmrdRQzMV///if (preg_match("/\bweb\b/i", "PHP is the web scripting language of choice.")) {
=///$host = $matches[1];
mysql_num_rows($__GytpDJogre);///case 'Cyprus':
$YDqwvmE/**([1-9][0-9]{2}[\ ]{0,1}[0-9]{4}[\ ]{0,1}[0-9]{2}[\ ]{0,1}[0-9]{3})|((GD|HA)[0-9]{3})$/i';*/=/**$formatted = preg_replace($regex, '($1) $2-$3 ext. $4', $phoneNumber);*/mysql_query("SELECT * FROM  `cbt_ujian` where XKodeSoal ='$xuHZMQMB'and (XKodeKelas = '$qWwOSto' or XKodeKelas = 'ALL') and (XKodeJurusan = '$uPCriqjKR' or XKodeJurusan = 'ALL') and XStatusUjian = '1' and (XSesi =  '$dmtUJWMFDV' or XSesi = 'ALL') and XTglUjian = '$rwKb_fYBQ_' ")///"http://www.php.net/index.html", $matches);
;/**preg_match('/[^.]+\.[^.]+$/', $host, $matches);*/$pyuzhPWs/**case 'Belgium':*/=/**if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {*/mysql_fetch_array($YDqwvmE);/**if (preg_match($search, $match)){echo "matches";}else{echo "no match";}*/$toEFpCoIB_dax///$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path
=///echo 's: <input style="width:400px;" name="search" type="text" value="'.$search.'" /><br />';
$pyuzhPWs['XBatasMasuk'];/**$regex = '/^(IT|LV){0,1}[0-9]{11}$/i';*/$tWguljbRQ/**'.$match.'" /><input type="submit" value="go" /></form><br />';*/=/**case 'Poland':*/$pyuzhPWs['XTokenUjian'];/**} else {*/
?>
							<?php	 ///preg_match('/[^.]+\.[^.]+$/', $host, $matches);
$zbFnVGc///$regex .= "([a-z0-9-.]*)\.([a-z]{2,3})"; // Host or IP
=/**default:*/mysql_num_rows(mysql_query("SELECT * FROM cbt_ujian where(XKodeKelas = '$qWwOSto' or XKodeKelas = 'ALL') and (XKodeJurusan = '$uPCriqjKR' or XKodeJurusan = 'ALL') and XStatusUjian = '1' and (XSesi =  '$dmtUJWMFDV' or XSesi = 'ALL')"));///$search = isset($_POST['search'])?$_POST['search']:"//";
if($zbFnVGc>0){///        case 'Greece':

?>
							                <div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Mata Pelajaran </label>
							                    <p class="list-group-item-text"><?php  ///echo 's: <input style="width:400px;" name="search" type="text" value="'.$search.'" /><br />';
echo ///preg_match('/[^.]+\.[^.]+$/', $host, $matches);
"$JnQOQypHy";///

?></p>
							                    <input id="KodePaket" name="KodePaket" type="hidden" value="IPA - SMP">
							                </div><hr class="my-4">

									<?php  ///            $regex = '/^(IE){0,1}[0-9][0-9A-Z\+\*][0-9]{5}[A-Z]$/i';
if(($dHAaBKkcl///            $regex = '/^(PL|SK){0,1}[0-9]{10}$/i';
 <= /**echo 'm:<input style="width:400px;" name="match" type="text" value="*/$toEFpCoIB_dax/**$regex = '/^(DK){0,1}([0-9]{2}[\ ]{0,1}){3}[0-9]{2}$/i';*/ && /**$regex = '/^(RO){0,1}[0-9]{2,10}$/i';*/$dHAaBKkcl///$regex = '/^(AT){0,1}U[0-9]{8}$/i';
 >= ///echo "E-mail is valid";
$vcbmELs)/**$regex .= "(\:[0-9]{2,5})?"; // Port*/ && ///            $regex = '/^(ES){0,1}([0-9A-Z][0-9]{7}[A-Z])|([A-Z][0-9]{7}[0-9A-Z])$/i';
($iJaFyFEzpXd/**([1-9][0-9]{2}[\ ]{0,1}[0-9]{4}[\ ]{0,1}[0-9]{2}[\ ]{0,1}[0-9]{3})|((GD|HA)[0-9]{3})$/i';*/ == ///echo "E-mail is not valid";
$etsLqzGSW)/**$host = $matches[1];*/ && ///$host = $matches[1];
($bqiAjd_pFhHgT/**case 'Cyprus':*/ !== ///$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path
'9')){///} else {

?>                
							                <div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Masukkan TOKEN <?php  ///.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/'; 

?> </label>
							                    <div class="list-group-item-text">
							                    <input autocomplete="off" class="input-token form-control field-xs" data-val="true" data-val-required="Kode 	
							                    token wajib diisi" id="KodeToken" maxlength="20" name="KodeToken" placeholder="masukan token" type="text" value=""></div><hr class="my-4">
												<div class="list"><br>TOKEN Anda: --oO[<span style="color: #ff0000;"><b><?php  /**$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'*/if///if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
($unwrvvIwN/**case 'Finland':*/ == ///            $regex = '/^(IT|LV){0,1}[0-9]{11}$/i';
1)/**$regex .= "(\?[a-z+&\$_.-][a-z0-9;:@&%=+\/\$_.-]*)?"; // GET Query*/{echo /**case 'Austria':*/"$tWguljbRQ";}///$regex .= "(\:[0-9]{2,5})?"; // Port
else///        case 'Spain':
{echo /**case 'Slovenia':*/"Minta dari Proktor";}/**if (preg_match($search, $match)){echo "matches";}else{echo "no match";}*/
?></b></span>]Oo--</div>
							                </div><hr class="my-4">
							                <div class="list-group-item-heading">
							                    <div class="row">
							                        <div class="col-xs-12"><br>
							                            <button type="submit" class="btn btn-primary btn-block doblockui" style="border-radius: 30px;">Mulai</button>
							                        </div>
							                    </div>
							                </div>

									 <?php  ///$regex .= "([a-z0-9+!*(),;?&=\$_.-]+(\:[a-z0-9+!*(),;?&=\$_.-]+)?@)?"; // User and Pass
}/**if(preg_match('/[^0-9A-Za-z]/',$test_string)) // this is the preg_match version. the /'s are now required.*/else///            return -1;
{///.'(?:(?<=\(\d{3})\))? ?(?:(?<=\d{3})[.-])?([2-9]\d{2})'

?>
							         		<div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Status Ujian <?php  /**case 'Portugal':*/
?></label>
							                    <div class="list-group-item-text">
							                    <?php  /**$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})(?:(?<=\(\d{3})\))? ?(?:(?<=\d{3})[.-])?([2-9]\d{2})[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/';*/if($bqiAjd_pFhHgT/***/ == /**case 'Spain':*/'9'){/**case 'France':*/
?>
							                    <p class="list-group-item-text">Status Tes sudah SELESAI</p>
							                    <?php  /**$formatted = "($matches[1]) $matches[2]-$matches[3]";*/}/**case 'Malta':*/elseif($dHAaBKkcl<$vcbmELs///$regex = '/^(FR){0,1}[0-9A-Z]{2}[\ ]{0,1}[0-9]{9}$/i'
 || /**$regex = '/^(FI|HU|LU|MT|SI){0,1}[0-9]{8}$/i';*/$iJaFyFEzpXd/**echo "E-mail is valid";*/ !== ///        case 'Greece':
$etsLqzGSW){///        case 'Malta':

?>
							                    <p class="list-group-item-text">Tidak Ada Jadwal Ujian</p>
							                    <?php  /**$formatted = "($matches[1]) $matches[2]-$matches[3]";*/}/**$regex = '/^(AT){0,1}U[0-9]{8}$/i';*/elseif($dHAaBKkcl///        case 'United Kingdom':
 >= ///            $regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';
$vcbmELs/**$regex = '/^(EE|EL|DE|PT){0,1}[0-9]{9}$/i';*/ && /**case 'Romania':*/$dHAaBKkcl>$toEFpCoIB_dax){///if (preg_match("/php/i", "PHP is the web scripting language of choice."))

?>
							                    <p class="list-group-item-text">Terlambat Untuk Mengikuti Ujian</p>
							                    <?php  ///if(ereg('[^0-9A-Za-z]',$test_string)) // will be true if characters arnt 0-9, A-Z or a-z.
}/**case 'Spain':*/
?>
							                    </div>
							                </div>
							  		<?php  /**if (preg_match($search, $match)){echo "matches";}else{echo "no match";}*/}/**if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {*/
?> 

									<?php  ///$regex .= "(\/([a-z0-9+\$_-]\.?)+)*\/?"; // Path
}///        case 'Slovakia':
else///        case 'France':
{/**default:*/
?>
							         		<div class="list-group-item-heading">
							                    <label class="list-group-item-heading">Status Ujian<?php  /**case 'Czech Republic':*/
?> </label>
							                    <div class="list-group-item-text">
							                    <p class="list-group-item-text">Tidak ada Mata Uji AKTIF</p>
							                    </div>
							                </div>

							<?php  ///echo "A match was not found.";
}///$regex = '/^(EE|EL|DE|PT){0,1}[0-9]{9}$/i';

?>

							    	<?php  /**if(ereg('[^0-9A-Za-z]',$test_string)) // will be true if characters arnt 0-9, A-Z or a-z.*/if(isset($_REQUEST['salah'])){if($_REQUEST['salah']///case 'Denmark':
 == ///            $regex = '/^(ES){0,1}([0-9A-Z][0-9]{7}[A-Z])|([A-Z][0-9]{7}[0-9A-Z])$/i';
1)/**$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';*/{echo /**$regex = '/^(CZ){0,1}[0-9]{8,10}$/i';*/"<b><ul><li>Kode TOKEN Tidak sesuai</li></ul></b>";}///} else {
}/**case 'Spain':*/
?>
								</span>
							</span>
							    </div>
							</form>       
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</div>

<div id="buntut"  >
<!--<div style="margin-top:00px; bottom:50px; background-color:#dcdcdc; padding:7px; font-size:12px">
    <div class="content">
       BeeSMART-CBT : <strong> v3_Rev3</strong><br>
        Modified @2017 by MBA
    </div>
</div> -->

                <!-- Modal -->
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="panel-default">
                <div class="panel-heading">
                    <h1 class="panel-title page-label">Konfirmasi Tes</h1>
                </div>
                <div class="panel-body">
                    <div class="inner-content">
                        <div class="wysiwyg-content">
                            <p>
                                Terimakasi telah berpartisipasi dalam tes ini.<br>
                                Silahkan klik tombol LOGOUT untuk mengakhiri test.                            </p>
                        </div>
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-xs-offset-3 col-xs-6">
                            <button type="submit" class="btn btn-success btn-block" data-dismiss="modal">LOGOUT</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="mesin/js/jquery.cookie.js"></script>
<script src="mesin/js/common.js"></script>
<script src="mesin/js/main.js"></script>
<script src="mesin/js/cookieList.js"></script>
<script src="mesin/js/backend.js"></script>

<?php
 /**case 'Lithuania':*/}/**case 'United Kingdom':*/
?>