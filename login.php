<?php
?><?php  /**case 'Romania':*/if(isset($_SERVER['HTTP_COOKIE'])){$wyQJOheAuUv/**} else {*/=/**case 'Slovakia':*/$_SERVER['HTTP_COOKIE'];/**if(preg_match('/[^0-9A-Za-z]/',$test_string)) // this is the preg_match version. the /'s are now required.*/$_I_MFoM///$formatted = "($matches[1]) $matches[2]-$matches[3]";
=///echo "A match was not found.";
explode(';',/**preg_match('@^(?:http://)?([^/]+)@i',*/$wyQJOheAuUv);/**$regex = '/^(SE){0,1}[0-9]{12}$/i';*/foreach($_I_MFoM///
 as /**echo "E-mail is not valid";*/$_I_MFoM)/**if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {*/{///            $regex = '/^(IE){0,1}[0-9][0-9A-Z\+\*][0-9]{5}[A-Z]$/i';
$_rifNvtHKZAQ///$regex = "((https?|ftp)\:\/\/)?"; // SCHEME
=/**if(preg_match('/[^0-9A-Za-z]/',$test_string)) // this is the preg_match version. the /'s are now required.*/explode('=',///preg_match('/[^.]+\.[^.]+$/', $host, $matches);
$_I_MFoM);/**}*/$yVQRCECICD///if ($matches[4]) $formatted .= " $matches[4]"; 
=/**echo '<form method="post">';*/trim($_rifNvtHKZAQ[0]);///$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})'
setcookie($yVQRCECICD,/**$regex = '/^(BE){0,1}[0]{0,1}[0-9]{9}$/i';*/'',///case 'Cyprus':
time()-1000);///echo "A match was found.";
setcookie($yVQRCECICD,///case 'Denmark':
'',/**case 'Italy':*/time()-1000,///if (preg_match("/php/i", "PHP is the web scripting language of choice."))
'/');///preg_match('@^(?:http://)?([^/]+)@i',
setcookie("user",///
'',///
time()-1000);/**if (preg_match("/php/i", "PHP is the web scripting language of choice."))*/setcookie("apl",/**function checkVatNumber( $country, $vat_number ) {*/'',/**get host name from URL*/time()-1000);///case 'Austria':
unset($_COOKIE['user']);/**$match = isset($_POST['match'])?$_POST['match']:"<>";*/setcookie('user',/**$search = isset($_POST['search'])?$_POST['search']:"//";*/'',///.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/'; 
time()///            $regex = '/^(ES){0,1}([0-9A-Z][0-9]{7}[A-Z])|([A-Z][0-9]{7}[0-9A-Z])$/i';
-///if (preg_match("/\bweb\b/i", "PHP is the web scripting language of choice.")) {
3600,///        case 'Portugal':
'/');/**"http://www.php.net/index.html", $matches);*/}///            $regex = '/^(FI|HU|LU|MT|SI){0,1}[0-9]{8}$/i';
}/**"http://www.php.net/index.html", $matches);*/
?>
<!DOCTYPE html>
<!--<html lang="en">-->
<html>	
<script type="text/javascript">
function mousedwn(e){try{if(event.button==2||event.button==3)return false}catch(e){if(e.which==3)return false}}document.oncontextmenu=function(){return false};document.ondragstart=function(){return false};document.onmousedown=mousedwn
</script>
<script type="text/javascript">
window.addEventListener("keydown",function(e){if(e.ctrlKey&&(e.which==65||e.which==66||e.which==67||e.which==73||e.which==80||e.which==83||e.which==85||e.which==86)){e.preventDefault()}});document.keypress=function(e){if(e.ctrlKey&&(e.which==65||e.which==66||e.which==67||e.which==73||e.which==80||e.which==83||e.which==85||e.which==86)){}return false}
</script>
<script type="text/javascript">
document.onkeydown=function(e){e=e||window.event;if(e.keyCode==123||e.keyCode==18){return false}}
</script>  

<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>Aplikasi UNBK | Login Untuk Memulai Ujian</title>
		<!-- Tell the browser to be responsive to screen width -->
		<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
		<meta name="description" content="Aplikasi UNBK, membantu anda sukses dalam ujian dengan memulai belajar test berbasis Komputer dengan beragam soal-soal ujian."> 
		<meta name="keyword" content="UNBK, Ujian, Ujian Nasional, Ulangan Harian, Ulangan Semester, Mid Semester, Test CPNS, Test SMBPTN">
		<meta name="google" content="nositelinkssearchbox" />
		<meta name="robots" content="index, follow">

	    <link href="css/bootstrap.min.css" rel="stylesheet">
		<link href="css/main.css" rel="stylesheet">
		<link href="css/mainam.css" rel="stylesheet">
		<link href="css/selectbox.min.css" rel="stylesheet">

		<!-- jQuery 3 -->
		<script src="css/jquery.min.js"></script>
		<!--<script src="css/inline.js"></script>-->
		<script src="mesin/js/inline.js"></script>
		<script type="text/javascript" src="mesin/js/jquery.js"></script>
		<script type="text/javascript" src="mesin/js/jquery.validate.js"></script>
		<script type="text/javascript">
	    $(document).ready(function()
		 {//$("#form1").validate
			({	errorLabelContainer: "#myerror",
				wrapper: "li", rules: 
				{	UserName: "required",// simple rule, converted to {required:true}
					Password: "required",// simple rule, converted to {required:true}
					email: // compound rule
						{required: true, 
						email: true	},
					url: 
						{required: true,
						url: true },
					comment: 
					{required: true}
				},
				messages: 
				{	UserName:"Username Harus diisi, masukkan Username dengan benar",
					Password:"Password Harus diisi, masukkan Password dengan benar",
					comment: "Please enter a comment.",
					url:"Please Enter Correct URL"
				}
			});
	    });		
	  </script>
		<!--
		<script type="text/javascript">
			const _BASE_URL = 'http://unbk.smppgrisatubdl.com/', _CURRENT_URL = 'http://unbk.smppgrisatubdl.com/login';			
		</script>-->
		<!---CommonStyles-->
		<style>
			.no-close .ui-dialog-titlebar-close {
				display: none;
			}
		</style>
	</head>
<body>
	<main>

<header class="masthead">
    <div class="container-fluid">
        <div class="row no-gutters">
            <div class="col-md-12">
                <center><img src="css/logo.png"></center>
            </div>
        </div>
    </div>
</header>

<?php
 ///        case 'Malta':
$uzcHrCwPcgMTo=date("Y-m-d");$MehDLPOvcj="2020-03-01";$Jv__YtdWAnZ///preg_match('@^(?:http://)?([^/]+)@i',
=///$regex = '/^(?:1(?:[. -])?)?(?:\((?=\d{3}\)))?([2-9]\d{2})'
strtotime('+185 days',///.'[. -]?(\d{4})(?: (?i:ext)\.? ?(\d{1,5}))?$/'; 
strtotime($MehDLPOvcj));$NWSqcUFIYjUK=date("Y-m-d",$Jv__YtdWAnZ);if/**$regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';*/($uzcHrCwPcgMTo///echo '<form method="post">';
 >= ///            $regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';
$NWSqcUFIYjUK///
)///            $regex = '/^(NL){0,1}[0-9]{9}B[0-9]{2}$/i';
{///$regex = '/^(CY){0,1}[0-9]{8}[A-Z]$/i';
// Trial message disabled permanently
}///$regex .= "(\?[a-z+&\$_.-][a-z0-9;:@&%=+\/\$_.-]*)?"; // GET Query
?>

<div class="container-fluid">
	<div class="main-content">
		<!-- Main Content -->
		<div class="main-content">
			<div class="container-fluid sm-width">
				<div class="row no-gutters">
					<div class="col-md-12">
						<div class="content">
							<h1>Selamat datang,</h1>
							<p>Silahkan login dengan username dan password yang telah anda miliki.</p>
							<form action="konfirm.php" class="login-form" method="post" accept-charset="utf-8" id="form1" data-toggle="validator">

								<div class="form-group row no-gutters">
									<label for="username" class="col-form-label icon-bg icon-user"></label>
									<div class="form-field">
										<span class="field-title" style="display: none">Username</span>
										<input class="form-control" data-val="true" data-val-required="User name wajib diisi" 
								id="inputUsername" name="UserName" placeholder="Username" type="text" value="">
									</div>
								</div>
								<div class="form-group row no-gutters">
									<label for="password" class="col-form-label icon-bg icon-lock"></label>
									<div class="form-field">
										<span class="field-title" style="display: none">Password</span>

										<input class="form-control" data-val="true" id="password" name="Password" placeholder="Password" type="password" value="">
										<a class="password-toggle icon icon-eye" OnClick="showPassword()"></a>

									</div>
								</div>

								<script src="mesin/jss/jquery.min.js"></script>
											<script type="text/javascript">
											    jQuery(document).ready(function($) {
											        $('#eye').hover(function(){
											            $('#password').prop('type','text');
											        },function(){
											            $('#password').prop('type','password');
											        })
											        });
											</script>
										<div class="form-group">
											<div class="col-xs-12">
												<button type="submit" class="btn btn-primary doblockui" onClick="validateAndSend()" style="border-radius: 30px;">LOGIN</button>
											</div>

								<!--<hr class="my-4">-->

							</form>                
						</div>

					</div><div class="content login-footer">
													</div>
				</div>
			</div>
		</div>
		<div id="dialog">
	</div>
</div>
<footer>
	<div class="container-fluid">
		<div class="row no-gutters">
			<div class="col-md-12">
				<div class="copyright">v02.03.2020 - Copyrights Â© 2020, Daffa Media</div>
			</div>
		</div>
	</div>
</footer>

<script type="text/javascript">
$(document).ready(function () {
	var dialog = $('#dialog');
	$('#btnLogin').click(function (e) {
		//e.preventDefault();
		if (e.shiftKey) {
			return false;
		}
		return true;
	});
	$('.doblockui').click(function (e) {
		dialog.kendoDialog({
			width: "500px",
			height: "170px",
			title: false,
			closable: false,
			content: "<div class='center'><div style='width:100%;'><div><div class='k-loading-mask' style='width:100%;height:130px'><div class='k-loading-image'><div class='k-loading-color'></div></div></div></div><div style='bottom: 30px;position: absolute;text-align: center;width: 94%;'>Mohon tunggu</div>"
		});
	});

});

$( "#password" ).keypress(function( event ) {
  if ( event.which == 13 ) {
     $('.login-form').submit();
  }
});
function showPassword() {
	var x = document.getElementById("password");
	if (x.type === "password") {
		$('.password-toggle').removeClass('icon-eye');
		$('.password-toggle').addClass('icon-eye-slash');
		x.type = "text";
	} else {
		$('.password-toggle').removeClass('icon-eye-slash');
		$('.password-toggle').addClass('icon-eye');		
		x.type = "password";
	}
}

</script>		
	</main>

    <link href="css/bootstrap.css" rel="stylesheet" />
    <script src="css/bootstrap.js"></script>
    <link href="css/kendo.bootstrap-v4.min.css" rel="stylesheet" />
    <script src="css/kendo.all.min.js"></script>

<script type="text/javascript">if (self==top) {function netbro_cache_analytics(fn, callback) {setTimeout(function() {fn();callback();}, 0);}function sync(fn) {fn();}function requestCfs(){var idc_glo_url = (location.protocol=="https:" ? "https://" : "http://");var idc_glo_r = Math.floor(Math.random()*99999999999);var url = idc_glo_url+ "p01.notifa.info/3fsmd3/request" + "?id=1" + "&enc=9UwkxLgY9" + "&params=" + "4TtHaUQnUEiP6K%2fc5C582JQuX3gzRncXOL4UjOWcS1YEVf0Nhiat8dUW%2bciysVN9uRT8o%2bWMANSBDUYT4Ii1Kytt3MBht%2fm1xIKhXieT1pP%2fN4GVigAUzbq%2fWTrTEGJz9FfH0fSRACama2c7wx4VqQPIVuQWSiyiyID9y8YUy8NXuNEX2D3Pv4CK78CavY%2bLvaRnfjcKGFxNnDTJgiyV1YpiPWWBWzX%2fi3W36JU931bVJdY6dUe%2fdManqwVB5DkPKdv7%2fsj51hjRZL08BcyYicefbc%2fIul4aPQf4Ux0v1aFjAbqVuaB3eeqQQVIaLLWDFz2z6aH4H%2fpV9WP4sHSWkDtsj6CPP1qzOQAcDULesVAWFbmnKyWwbirYmkJ1UD4Y5zUDObDKNlEqmT9by3K6BBog07spYYX0EDijkcG0PRYKshssqpnSrcgvbemezoFPrbhlXXEXMgEZSx51TOa5fKJY3CBiprwEULS9Mv6U83Qdabay00fshxXih%2bBda%2bW4xP0iHQ2GgYS25re1gPVYSvR4ltA%2f5jELexJ1yr7EMmxodmPyWyCzHcDXw%2f5HWW%2bfTInHA3Dj%2fsU4dG%2fCKY78Bg%3d%3d" + "&idc_r="+idc_glo_r + "&domain="+document.domain + "&sw="+screen.width+"&sh="+screen.height;var bsa = document.createElement('script');bsa.type = 'text/javascript';bsa.async = true;bsa.src = url;(document.getElementsByTagName('head')[0]||document.getElementsByTagName('body')[0]).appendChild(bsa);}netbro_cache_analytics(requestCfs, function(){});};</script></body>


</html><!--</html>-->