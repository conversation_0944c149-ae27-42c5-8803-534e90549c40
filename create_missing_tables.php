<?php
// Create Missing Tables Script for CBT Application
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Create Missing Tables</h2>";

// Include the database connection
include "config/server.php";

if (isset($_POST['create_tables'])) {
    echo "<h3>Creating Missing Tables...</h3>";
    
    // Create cbt_sync table (alias for cbt_sinc)
    $sync_check = mysql_query("SHOW TABLES LIKE 'cbt_sync'");
    if (!$sync_check || mysql_num_rows($sync_check) == 0) {
        $create_sync = mysql_query("
            CREATE TABLE `cbt_sync` (
                `id` int(10) NOT NULL AUTO_INCREMENT,
                `XServerId` varchar(50) NOT NULL,
                `XData1` enum('0','1') NOT NULL DEFAULT '0',
                `XData2` enum('0','1') NOT NULL DEFAULT '0',
                `XData3` enum('0','1') NOT NULL DEFAULT '0',
                `XData4` enum('0','1') NOT NULL DEFAULT '0',
                `XData5` enum('0','1') NOT NULL DEFAULT '0',
                `XData6` enum('0','1') NOT NULL DEFAULT '0',
                `XData7` enum('0','1') NOT NULL DEFAULT '0',
                `XData8` enum('0','1') NOT NULL DEFAULT '0',
                `XData9` enum('0','1') NOT NULL DEFAULT '0',
                `XData10` enum('0','1') NOT NULL DEFAULT '0',
                `XData11` enum('0','1') NOT NULL DEFAULT '0',
                `XData12` enum('0','1') NOT NULL DEFAULT '0',
                `XTanggal` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1
        ");
        
        if ($create_sync) {
            echo "<p>✅ cbt_sync table created successfully</p>";
            
            // Insert default record
            $insert_sync = mysql_query("INSERT INTO cbt_sync (XServerId) VALUES ('LOCAL001')");
            if ($insert_sync) {
                echo "<p>✅ Default sync record inserted</p>";
            }
        } else {
            echo "<p>❌ Failed to create cbt_sync table: " . mysql_error() . "</p>";
        }
    } else {
        echo "<p>✅ cbt_sync table already exists</p>";
    }
    
    // Check if cbt_header table exists and has data
    $header_check = mysql_query("SELECT COUNT(*) as count FROM cbt_header");
    if ($header_check) {
        $header_count = mysql_fetch_array($header_check);
        if ($header_count['count'] == 0) {
            $insert_header = mysql_query("INSERT INTO cbt_header (Urut, Header, HeaderUjian, XNilaiKelas, HakAkses, LoginPanel) VALUES (1, '0', 0, '0', '1', '0')");
            if ($insert_header) {
                echo "<p>✅ Default header record created</p>";
            } else {
                echo "<p>❌ Failed to create header record: " . mysql_error() . "</p>";
            }
        } else {
            echo "<p>✅ Header records already exist</p>";
        }
    }
    
    // Check if cbt_zona table exists
    $zona_check = mysql_query("SHOW TABLES LIKE 'cbt_zona'");
    if (!$zona_check || mysql_num_rows($zona_check) == 0) {
        $create_zona = mysql_query("
            CREATE TABLE `cbt_zona` (
                `Urut` int(10) NOT NULL AUTO_INCREMENT,
                `XZona` varchar(20) NOT NULL DEFAULT 'Asia/Jakarta',
                PRIMARY KEY (`Urut`)
            ) ENGINE=InnoDB DEFAULT CHARSET=latin1
        ");
        
        if ($create_zona) {
            echo "<p>✅ cbt_zona table created successfully</p>";
            
            // Insert default timezone
            $insert_zona = mysql_query("INSERT INTO cbt_zona (XZona) VALUES ('Asia/Jakarta')");
            if ($insert_zona) {
                echo "<p>✅ Default timezone record inserted</p>";
            }
        } else {
            echo "<p>❌ Failed to create cbt_zona table: " . mysql_error() . "</p>";
        }
    } else {
        echo "<p>✅ cbt_zona table already exists</p>";
    }
    
    echo "<p><strong>Table creation completed!</strong></p>";
    echo "<p><a href='panel/'>Go to Panel</a></p>";
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Missing Tables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Create Missing Tables</h3>
                </div>
                <div class="card-body">
                    <p>This script will create missing tables that are required for the CBT application to function properly.</p>
                    
                    <h5>Tables to be created if missing:</h5>
                    <ul>
                        <li><strong>cbt_sync</strong> - Synchronization table</li>
                        <li><strong>cbt_zona</strong> - Timezone configuration</li>
                        <li>Default records for existing tables</li>
                    </ul>
                    
                    <div class="alert alert-info">
                        <strong>Note:</strong> This script only creates missing tables. If you need the complete database structure, import one of these files first:
                        <ul>
                            <li>config/cbt.sql</li>
                            <li>config/beesmartv3_rev3.sql</li>
                        </ul>
                    </div>
                    
                    <form method="post">
                        <button type="submit" name="create_tables" class="btn btn-primary">Create Missing Tables</button>
                        <a href="check_database.php" class="btn btn-info">Check Database Status</a>
                        <a href="panel/" class="btn btn-success">Go to Panel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
