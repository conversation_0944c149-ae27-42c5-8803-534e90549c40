<?php
// Include the new database connection layer
require_once "database.php";
include "ipserver.php";

// Connect using new Database class
try {
    $database = Database::getInstance($host_name, $user_name, $password);
    $link = $database->getConnection();
    $status_konek = "1";
} catch (Exception $e) {
    $status_konek = "0";
    $link = false;
}

// Backward compatibility functions - only declare if not already declared
if (!function_exists('mysql_connect')) {
    function mysql_connect($host, $user, $pass) {
        try {
            $database = Database::getInstance($host, $user, $pass);
            return $database ? $database->getConnection() : false;
        } catch (Exception $e) {
            return false;
        }
    }
}
else {
$status_konek = "1";
mysql_select_db($database, $link) or die('<b><span style="color: #ff0000;">SERVER tidak terhubung ke DataBase SERVER PUSAT</span><br><br>SOLUSI : <br></br>Edit koneksi database BEESMART-CBT dengan tepat</b> >> Langkah :
<li>Klik <span style="color: #0A7BFF;">Setting Server</span> => <span style="color: #CCA600;">Setting Server Pusat</span> => ubah nama Database, Username Db dan Password dengan tepat sesuai database Server Pusat</li>
<br><b>Edit Id/Kode Sekolah dengan tepat </b> >> Langkah :
<li> Klik <span style="color: #0A7BFF;">Data Sekolah </span> => <span style="color: #CCA600;">Identitas Sekolah </span>=> ubah data dengan tepat sesuai data yang ada di Sever Pusat');
//echo "Koneksi Terbuka";

}


date_default_timezone_set("$zo");

//date_default_timezone_set("Asia/Jakarta");
//mysql_select_db("beesmartv3_rev1", $sqlconn);
//tambahkan IPPublic ke Remote MySQL di CPanel hosting


?>
