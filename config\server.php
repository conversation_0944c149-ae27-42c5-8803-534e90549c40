<?php
// Include the new database connection layer
require_once "database.php";
include "db_server.php";

$teks3="cbt";
// 0. Tentukan/Buat Database yang akan digunakan
if ($db_server==""){
	$teks1="<?php ";
	$teks2="\$db_server=\"";
	$teks4="\";";
	$buat_db_server =$teks1.$teks2.$teks3.$teks4; $file = fopen("../../config/db_server.php","w");
	if($file){fputs($file,$buat_db_server);}
	fclose($file);
	$db=$teks3;
}else{$db=$db_server;}

$db_host = "localhost:3306";
$db_user="root";
$db_pass="";

// 1. Connect ke database using new Database class
$database = Database::getInstance($db_host, $db_user, $db_pass, $db);
$sqlconn = $database->getConnection();

// 2. Database is already selected in the Database class constructor
$db_selected = true;

// Backward compatibility functions - only declare if not already declared
if (!function_exists('mysql_connect')) {
    function mysql_connect($host, $user, $pass) {
        global $database;
        $database = Database::getInstance($host, $user, $pass);
        return $database ? true : false;
    }
}

if (!function_exists('mysql_select_db')) {
    function mysql_select_db($db, $connection = null) {
        global $database;
        return $database->selectDb($db);
    }
}

if (!function_exists('mysql_query')) {
    function mysql_query($sql, $connection = null) {
        global $database;
        return $database->query($sql);
    }
}

if (!function_exists('mysql_fetch_array')) {
    function mysql_fetch_array($result) {
        global $database;
        return $database->fetchArray($result);
    }
}

if (!function_exists('mysql_num_rows')) {
    function mysql_num_rows($result) {
        global $database;
        return $database->numRows($result);
    }
}

if (!function_exists('mysql_error')) {
    function mysql_error() {
        global $database;
        return $database->error();
    }
}

if (!function_exists('mysql_close')) {
    function mysql_close($connection = null) {
        global $database;
        return $database->close();
    }
}

if (!function_exists('mysql_real_escape_string')) {
    function mysql_real_escape_string($string, $connection = null) {
        global $database;
        return $database->escapeString($string);
    }
}

if (!function_exists('mysql_insert_id')) {
    function mysql_insert_id($connection = null) {
        global $database;
        return $database->insertId();
    }
}

if (!function_exists('mysql_get_server_info')) {
    function mysql_get_server_info($connection = null) {
        global $database;
        if ($database) {
            return $database->getServerInfo();
        }
        return 'Unknown';
    }
}

if (!function_exists('mysql_affected_rows')) {
    function mysql_affected_rows($connection = null) {
        global $database;
        if ($database) {
            return $database->affectedRows();
        }
        return 0;
    }
}

if (!function_exists('mysql_errno')) {
    function mysql_errno($connection = null) {
        global $database;
        if ($database) {
            return $database->errno();
        }
        return 0;
    }
}

if (!function_exists('mysql_fetch_row')) {
    function mysql_fetch_row($result) {
        global $database;
        if ($database) {
            return $database->fetchRow($result);
        }
        return false;
    }
}

if (!function_exists('mysql_fetch_assoc')) {
    function mysql_fetch_assoc($result) {
        global $database;
        if ($database) {
            return $database->fetchAssoc($result);
        }
        return false;
    }
}

if (!function_exists('mysql_result')) {
    function mysql_result($result, $row, $field = 0) {
        global $database;
        if ($database) {
            return $database->result($result, $row, $field);
        }
        return false;
    }
}

// 3. Periksa database jika telah siap
$val = mysql_query('select * from cbt_admin LIMIT 1');
if($val == TRUE){
	$log = mysql_fetch_array($val);
	$skull= $log['XSekolah'];
	if ($log['XTingkat']=="SMA" || $log['XTingkat']=="MA"||$log['XTingkat']=="STM"){$rombel="Jurusan";}else{$rombel="Rombel";}
	
	if (mysql_query("select * from cbt_zona LIMIT 1")==TRUE){
	$xadmz = mysql_fetch_array(mysql_query("select * from cbt_zona LIMIT 1"));
	$zo= $xadmz['XZona'];
	}
	else {	$zo="Asia/Jakarta";}
	date_default_timezone_set($zo);
	$xadm = mysql_fetch_array(mysql_query("select * from cbt_server LIMIT 1"));
	$xserver= $xadm['XServer'];
	$mode = $xserver; 
}else{$skull="UBK/CBT"; date_default_timezone_set("Asia/Jakarta");}