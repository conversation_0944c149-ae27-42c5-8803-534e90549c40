<?php
// Include the new database connection layer
require_once "database.php";
include "ipserver.php";

// Connect using new Database class
$database = Database::getInstance($ipserver, $db_userm, $db_pasw, $db_nama);
$link = $database->getConnection();

if (!$link) {
    die('Koneksi-2 BEESMART-CBT belum di setting');
}

date_default_timezone_set("$zo");

// Backward compatibility functions for this file - only declare if not already declared
if (!function_exists('mysql_connect')) {
    function mysql_connect($host, $user, $pass) {
        global $database;
        $database = Database::getInstance($host, $user, $pass);
        return $database ? $database->getConnection() : false;
    }
}

if (!function_exists('mysql_select_db')) {
    function mysql_select_db($db, $connection = null) {
        global $database;
        return $database->selectDb($db);
    }
}

if (!function_exists('mysql_query')) {
    function mysql_query($sql, $connection = null) {
        global $database;
        return $database->query($sql);
    }
}

if (!function_exists('mysql_fetch_array')) {
    function mysql_fetch_array($result) {
        global $database;
        return $database->fetchArray($result);
    }
}

if (!function_exists('mysql_num_rows')) {
    function mysql_num_rows($result) {
        global $database;
        return $database->numRows($result);
    }
}

if (!function_exists('mysql_error')) {
    function mysql_error() {
        global $database;
        return $database->error();
    }
}